import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ApplicationsModule } from './applications/applications.module';
import { AuthModule } from './Auth/auth.module';
import { CitiesModule } from './cities/cities.module';
import { SharedModule } from './common/modules/shared.module';
import { XConfigService } from './common/services/xconfig.service';
import { ContactsModule } from './contacts/contacts.module';
import { DocumentsModule } from './documents/documents.module';
import { EmployeesModule } from './employees/employees.module';
import { EventLoggerModule } from './event-logger/event-logger.module';
import { OfficesModule } from './offices/offices.module';
import { PermissionsModule } from './permissions/permissions.module';
import { ProgramsModule } from './programs/programs.module';
import { RegionsModule } from './regions/regions.module';
import { RolesModule } from './roles/roles.module';
import { StudentsModule } from './students/students.module';
import { UniversitiesModule } from './universities/universities.module';
import { UsersModule } from './users/users.module';
import { WorkflowsModule } from './workflows/workflows.module';
import { CountryModule } from './country/country.module';


@Module({
  imports: [
    AuthModule,
    UsersModule,
    RolesModule,
    PermissionsModule,
    ConfigModule.forRoot(),
    CitiesModule,
    StudentsModule,
    EmployeesModule,
    RegionsModule,
    OfficesModule,
    ApplicationsModule,
    UniversitiesModule,
    ProgramsModule,
    SharedModule,
    EventLoggerModule,
    DocumentsModule,
    WorkflowsModule,
    ContactsModule,
    CountryModule,
  ],
  controllers: [AppController],
  providers: [AppService, XConfigService],
})
export class AppModule {}
