import { HttpException, HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from 'src/prisma/prisma.service';
import { CountryService } from './country.service';

describe('CountryService', () => {
  let service: CountryService;
  let prisma: PrismaService;

  const mockPrisma = {
    country: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CountryService,
        {
          provide: PrismaService,
          useValue: mockPrisma,
        },
      ],
    }).compile();

    service = module.get<CountryService>(CountryService);
    prisma = module.get<PrismaService>(PrismaService);
    jest.clearAllMocks();
  });

  describe('createACountry', () => {
    it('should create a country successfully', async () => {
      const dto = { name: 'TestCountry' };
      const expected = { id: '1', name: 'TestCountry' };

      mockPrisma.country.create.mockResolvedValue(expected);

      const result = await service.create(dto);
      expect(result).toEqual(expected);
      expect(prisma.country.create).toHaveBeenCalledWith({ data: dto });
    });

    it('should throw HttpException if create fails', async () => {
      const dto = { name: 'TestCountry' };

      mockPrisma.country.create.mockRejectedValue(new Error('Create failed'));

      await expect(service.create(dto)).rejects.toThrow(HttpException);
    });
  });

  describe('getAllCountries', () => {
    it('should return all countries', async () => {
      const expected = [{ id: '1', name: 'A' }, { id: '2', name: 'B' }];

      mockPrisma.country.findMany.mockResolvedValue(expected);

      const result = await service.findAll();
      expect(result).toEqual(expected);
      expect(prisma.country.findMany).toHaveBeenCalled();
    });

    it('should throw HttpException if findMany fails', async () => {
      mockPrisma.country.findMany.mockRejectedValue(new Error('Fetch failed'));

      await expect(service.findAll()).rejects.toThrow(HttpException);
    });
  });

  describe('getCountryById', () => {
    it('should return a country if found', async () => {
      const id = '1';
      const expected = { id, name: 'Test' };

      mockPrisma.country.findUnique.mockResolvedValue(expected);

      const result = await service.findOne(id);
      expect(result).toEqual(expected);
      expect(prisma.country.findUnique).toHaveBeenCalledWith({ where: { id } });
    });

    it('should throw NOT_FOUND if country does not exist', async () => {
      const id = '123';
      mockPrisma.country.findUnique.mockResolvedValue(null);

      await expect(service.findOne(id)).rejects.toThrowError(HttpException);
    });

    it('should throw HttpException if findUnique fails', async () => {
      const id = '1';
      mockPrisma.country.findUnique.mockRejectedValue(new Error('DB error'));

      await expect(service.findOne(id)).rejects.toThrow(HttpException);
    });
  });

  describe('updateACountry', () => {
    it('should update and return the updated country', async () => {
      const id = '1';
      const dto = { name: 'Updated' };
      const existing = { id, name: 'Old' };
      const updated = { id, name: 'Updated' };

      mockPrisma.country.findUnique.mockResolvedValue(existing);
      mockPrisma.country.update.mockResolvedValue(updated);

      const result = await service.update(id, dto);
      expect(result).toEqual(updated);
    });

    it('should throw NOT_FOUND if country does not exist', async () => {
      const id = '999';
      const dto = { name: 'DoesNotExist' };

      mockPrisma.country.findUnique.mockResolvedValue(null);

      await expect(service.update(id, dto)).rejects.toThrow(HttpException);
    });

    it('should throw HttpException if update fails', async () => {
      const id = '1';
      const dto = { name: 'FailUpdate' };
      const existing = { id, name: 'Old' };

      mockPrisma.country.findUnique.mockResolvedValue(existing);
      mockPrisma.country.update.mockRejectedValue(new Error('Update failed'));

      await expect(service.update(id, dto)).rejects.toThrow(HttpException);
    });
  });

  describe('deleteACountry', () => {
    it('should delete and return the deleted country', async () => {
      const id = '1';
      const existing = { id, name: 'ToDelete' };

      mockPrisma.country.findUnique.mockResolvedValue(existing);
      mockPrisma.country.delete.mockResolvedValue(existing);

      const result = await service.remove(id);
      expect(result).toEqual(existing);
    });

    it('should throw NOT_FOUND if country does not exist', async () => {
      const id = '999';
      mockPrisma.country.findUnique.mockResolvedValue(null);

      await expect(service.remove(id)).rejects.toThrow(HttpException);
    });

    it('should throw HttpException if delete fails', async () => {
      const id = '1';
      const existing = { id, name: 'ToDelete' };

      mockPrisma.country.findUnique.mockResolvedValue(existing);
      mockPrisma.country.delete.mockRejectedValue(new Error('Delete failed'));

      await expect(service.remove(id)).rejects.toThrow(HttpException);
    });
  });
});
