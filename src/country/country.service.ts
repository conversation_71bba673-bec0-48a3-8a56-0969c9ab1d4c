import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateCountryDto } from './dto/create-country.dto';
import { UpdateCountryDto } from './dto/update-country.dto';

@Injectable()
export class CountryService {
  constructor(private prisma: PrismaService) {}

  create(data: CreateCountryDto) {
    return this.prisma.country.create({ data });
  }

  findAll() {
    return this.prisma.country.findMany({
      include: {
        regions: true,
        offices: true,
        employees: true,
      },
    });
  }

  findOne(id: string) {
    return this.prisma.country.findUnique({
      where: { id },
      include: {
        regions: true,
        offices: true,
        employees: true,
      },
    });
  }

  async update(id: string, data: UpdateCountryDto) {
    const existingCountry = await this.prisma.country.findUnique({
      where: { id },
    });
    if (!existingCountry) {
      throw new NotFoundException(`Country with ID ${id} not found`);
    }
    return this.prisma.country.update({
      where: { id },
      data,
    });
  }

  async remove(id: string) {
    const country = await this.prisma.country.findUnique({
      where: { id },
    });
    if (!country) {
      throw new NotFoundException(`Country with ID ${id} not found`);
    }
    return this.prisma.country.delete({
      where: { id },
    });
  }
}
