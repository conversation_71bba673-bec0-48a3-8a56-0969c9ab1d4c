import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class XConfigService {
  private readonly logger: Logger;

  constructor(private configService: ConfigService) {
    this.logger = new Logger('ConfigService');

    // Log configuration values for debugging
    this.logger.log(`Running in ${this.environment} environment`);
    this.logger.log(`Auth Enabled: ${this.isAuthEnabled}`);
  }

  // Checks if authentication is enabled based on the environment or configuration
  get isAuthEnabled(): boolean {
    if (this.environment === 'production') return true; // In production, always enable auth
    const enableAuth = this.configService.get<string>('ENABLE_AUTH'); // Retrieve the config value
    return enableAuth ? enableAuth.toLowerCase() !== 'false' : false; // Check if the value isn't 'false'
  }

  // Retrieves the current environment (e.g., 'development', 'production')
  get environment(): string {
    return this.configService.get<string>('NODE_ENV') || 'development'; // Default to 'development' if undefined
  }
}
