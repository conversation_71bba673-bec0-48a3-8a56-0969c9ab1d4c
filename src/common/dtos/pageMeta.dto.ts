import { ApiProperty } from '@nestjs/swagger';
import { PageOptionsDto } from './pageOptions.dto';

export interface PageMetaDtoParams {
  itemCount: number;
  pageOptionsDto: PageOptionsDto;
}

export class PageMetaDto {
  @ApiProperty()
  readonly page: number;

  @ApiProperty()
  readonly take: number;

  @ApiProperty()
  readonly itemCount: number;

  @ApiProperty()
  readonly pageCount: number;

  @ApiProperty()
  readonly hasNextPage: boolean;

  @ApiProperty()
  readonly hasPreviousPage: boolean;

  constructor({ pageOptionsDto, itemCount }: PageMetaDtoParams) {
    this.page = pageOptionsDto.page;
    this.take = pageOptionsDto.take;
    this.itemCount = itemCount;
    this.pageCount = Math.ceil(this.itemCount / this.take);
    this.hasNextPage = this.page > 1;
    this.hasPreviousPage = this.page < this.pageCount;
  }
}
