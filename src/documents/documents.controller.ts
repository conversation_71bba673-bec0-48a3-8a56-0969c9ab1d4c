import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { GetDocumentDto } from './dto/get-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { PageOptionsDto } from 'src/common/dtos/pageOptions.dto';
import { ApiPaginatedResponse } from 'src/common/decorators/api-paginated-response.decorator';
import { PageDto } from 'src/common/dtos/page.dto';
import { Response } from 'express';

@ApiBearerAuth()
@ApiTags('Documents')
@Controller('documents')
export class DocumentsController {
  constructor(private readonly documentService: DocumentsService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The document file to upload',
        },
        type: {
          type: 'string',
          description: 'Type of the Document',
        },
        studentId: {
          type: 'string',
          format: 'uuid',
          description: 'ID of the Student this Document belongs to',
        },
        applicationId: {
          type: 'string',
          format: 'uuid',
          description: 'ID of Application',
        },
        comment: {
          type: 'string',
          description: 'Optional comment about the document',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, callback) => {
          const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
          const fileExtName = extname(file.originalname);
          const fileName = `${uniqueSuffix}${fileExtName}`;
          callback(null, fileName);
        },
      }),
    }),
  )
  @ApiResponse({
    status: 200,
    description: 'A new Document has been created successfully.',
    type: GetDocumentDto,
  })
  @Permissions('create:students')
  async createDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() createDocumentDto: CreateDocumentDto,
  ): Promise<GetDocumentDto> {
    return await this.documentService.createDocument(createDocumentDto, file);
  }

  @Get()
  @ApiPaginatedResponse(GetDocumentDto, 'All Documents of Student have been retrieved')
  @Permissions('read:documents')
  async findAllDocumentsByStudentId(
    @Query() pageOptionsDto: PageOptionsDto,
    @Query('studentId') studentId?: string,
  ): Promise<PageDto<GetDocumentDto>> {
    return await this.documentService.findAllDocumentsByStudentId(studentId, pageOptionsDto);
  }

  @Get(':id')
  @ApiResponse({
    status: 200,
    description: "A Document has been retrieved by its ID",
    type: GetDocumentDto,
  })
  @Permissions('read:students')
  async findDocumentById(@Param('id') id: string): Promise<GetDocumentDto> {
    return await this.documentService.findDocumentById(id);
  }

  @Patch(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UpdateDocumentDto })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, callback) => {
          const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
          const fileExtName = extname(file.originalname);
          const fileName = `${uniqueSuffix}${fileExtName}`;
          callback(null, fileName);
        },
      }),
    }),
  )
  @ApiResponse({
    status: 200,
    description: 'A Document has been updated successfully',
    type: GetDocumentDto,
  })
  @Permissions('update:students')
  async updateDocument(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() updateDocumentDto: UpdateDocumentDto,
  ): Promise<GetDocumentDto> {
    return await this.documentService.updateDocument(id, updateDocumentDto, file);
  }

  @Delete(':id')
  @ApiResponse({
    status: 200,
    description: 'A Document has been deleted successfully',
    type: GetDocumentDto,
  })
  @Permissions('delete:documents')
  async deleteDocument(@Param('id') id: string): Promise<GetDocumentDto> {
    return await this.documentService.deleteDocument(id);
  }

  @Get(':id/file')
  @ApiResponse({
    status: 200,
    description: 'Document file has been retrieved successfully',
    type: GetDocumentDto,
  })
  @Permissions('READ')
  @ApiRolesPermissions(['ADMIN', 'USER'], ['READ'])
  async getDocumentFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const document = await this.documentService.findDocumentById(id);
    const filePath = join(process.cwd(), 'uploads', document.fileUrl);
    return res.sendFile(filePath);
  }

  @Patch(':id/approve')
  @ApiResponse({
    status: 200,
    description: 'Document has been approved.',
    type: GetDocumentDto,
  })
  @Permissions('WRITE')
  @ApiRolesPermissions(['ADMIN'], ['WRITE'])
  async approveDocument(
    @Param('id') id: string,
    @Body('userId') userId: string,
  ): Promise<GetDocumentDto> {
    return await this.documentService.approveDocument(id, userId);
  }

  @Patch(':id/reject')
  @ApiResponse({
    status: 200,
    description: 'Document has been rejected.',
    type: GetDocumentDto,
  })
  @Permissions('WRITE')
  @ApiRolesPermissions(['ADMIN'], ['WRITE'])
  async rejectDocument(
    @Param('id') id: string,
    @Body('userId') userId: string,
    @Body('rejectionReason') rejectionReason?: string,
  ): Promise<GetDocumentDto> {
    return await this.documentService.rejectDocument(id, userId, rejectionReason);
  }

  @Get('filter')
  @ApiResponse({
    status: 200,
    description: 'Documents filtered by status.',
    type: [GetDocumentDto],
  })
  @Permissions('READ')
  @ApiRolesPermissions(['ADMIN'], ['READ'])
  async filterDocumentsByStatus(
    @Query('status') status: string,
  ): Promise<GetDocumentDto[]> {
    return await this.documentService.filterDocumentsByStatus(status);
  }
}
