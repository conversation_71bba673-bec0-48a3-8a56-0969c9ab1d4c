import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { GetDocumentDto } from './dto/get-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { PageDto } from 'src/common/dtos/page.dto';
import { PageMetaDto } from 'src/common/dtos/pageMeta.dto';
import { PageOptionsDto } from 'src/common/dtos/pageOptions.dto';
import { DocumentStatus } from '@prisma/client';

// Define allowed file types and max size
const ALLOWED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/png'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// Type for document update data
type DocumentUpdateData = {
  type?: string;
  studentId?: string;
  comment?: string;
  fileUrl?: string;
};

@Injectable()
export class DocumentsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new document
   * @param createDocumentDto - The document creation data
   * @param file - The uploaded file
   * @returns The created document
   * @throws {BadRequestException} If file type or size is invalid
   * @throws {NotFoundException} If student does not exist
   */
  async createDocument(
    createDocumentDto: CreateDocumentDto,
    file: Express.Multer.File,
  ): Promise<GetDocumentDto> {
    this.validateFile(file);

    try {
      const newDocument = await this.prisma.document.create({
        data: {
          type: createDocumentDto.type,
          fileUrl: file.filename,
          studentId: createDocumentDto.studentId,
          comment: createDocumentDto.comment,
          applicationId: createDocumentDto.applicationId,
        },
      });

      return newDocument;
    } catch (error) {
      this.handlePrismaError(error);
    }
  }

  /**
   * Finds a document by its ID
   * @param id - The document ID
   * @returns The found document
   * @throws {NotFoundException} If document is not found
   */
  async findDocumentById(id: string): Promise<GetDocumentDto> {
    try {
      const document = await this.prisma.document.findUnique({ where: { id } });
      if (!document) {
        throw new NotFoundException(`Document with ID ${id} not found`);
      }
      return document;
    } catch (error) {
      this.handlePrismaError(error);
    }
  }

  /**
   * Finds all documents for a student with pagination
   * @param studentId - The student ID
   * @param pageOptionsDto - Pagination options
   * @returns Paginated list of documents
   */
  async findAllDocumentsByStudentId(
    studentId: string,
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<GetDocumentDto>> {
    try {
      const [documents, itemCount] = await Promise.all([
        this.prisma.document.findMany({
          where: { studentId },
          skip: pageOptionsDto.skip,
          take: pageOptionsDto.take,
          orderBy: {
            createdAt: 'desc',
          },
        }),
        this.prisma.document.count({
          where: { studentId },
        }),
      ]);

      const pageMetaDto = new PageMetaDto({
        itemCount,
        pageOptionsDto,
      });

      return new PageDto(documents, pageMetaDto);
    } catch (error) {
      this.handlePrismaError(error);
    }
  }

  /**
   * Updates a document
   * @param id - The document ID
   * @param updateDocumentDto - The update data
   * @param file - Optional new file
   * @returns The updated document
   * @throws {NotFoundException} If document is not found
   * @throws {BadRequestException} If file type or size is invalid
   */
  async updateDocument(
    id: string,
    updateDocumentDto: UpdateDocumentDto,
    file?: Express.Multer.File,
  ): Promise<GetDocumentDto> {
    if (file) {
      this.validateFile(file);
    }

    const existingDocument = await this.findDocumentById(id);

    const updateData: DocumentUpdateData = {};
    if (updateDocumentDto.type) updateData.type = updateDocumentDto.type;
    if (updateDocumentDto.studentId) updateData.studentId = updateDocumentDto.studentId;
    if (updateDocumentDto.comment !== undefined) updateData.comment = updateDocumentDto.comment;
    if (file) updateData.fileUrl = file.filename;

    try {
      return await this.prisma.document.update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      this.handlePrismaError(error);
    }
  }

  /**
   * Deletes a document
   * @param id - The document ID
   * @returns The deleted document
   * @throws {NotFoundException} If document is not found
   */
  async deleteDocument(id: string): Promise<GetDocumentDto> {
    const existingDocument = await this.findDocumentById(id);

    try {
      await this.prisma.document.delete({ where: { id } });
      return existingDocument;
    } catch (error) {
      this.handlePrismaError(error);
    }
  }

  /**
   * Validates uploaded file
   * @param file - The file to validate
   * @throws {BadRequestException} If file is invalid
   */
  private validateFile(file: Express.Multer.File): void {
    if (!ALLOWED_FILE_TYPES.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${ALLOWED_FILE_TYPES.join(', ')}`,
      );
    }

    if (file.size > MAX_FILE_SIZE) {
      throw new BadRequestException(
        `File size exceeds maximum limit of ${MAX_FILE_SIZE / 1024 / 1024}MB`,
      );
    }
  }

  /**
   * Handles Prisma errors
   * @param error - The error to handle
   * @throws {NotFoundException} For not found errors
   * @throws {HttpException} For other errors
   */
  private handlePrismaError(error: any): never {
    if (error instanceof PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          throw new NotFoundException('Referenced record does not exist');
        case 'P2025':
          throw new NotFoundException('Record not found');
        default:
          throw new HttpException(
            'Database operation failed',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
      }
    }
    throw new HttpException(
      error.message || 'Internal server error',
      error.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }

  async approveDocument(id: string, userId: string): Promise<GetDocumentDto> {
    const document = await this.findDocumentById(id);
    if (!document) throw new NotFoundException('Document not found');
    if (document.status === 'APPROVED') throw new BadRequestException('Document already approved');
    return await this.prisma.document.update({
      where: { id },
      data: {
        status: 'APPROVED',
        approvedById: userId,
        approvedAt: new Date(),
        rejectionReason: null,
      },
    });
  }

  async rejectDocument(id: string, userId: string, rejectionReason?: string): Promise<GetDocumentDto> {
    const document = await this.findDocumentById(id);
    if (!document) throw new NotFoundException('Document not found');
    if (document.status === 'REJECTED') throw new BadRequestException('Document already rejected');
    return await this.prisma.document.update({
      where: { id },
      data: {
        status: 'REJECTED',
        approvedById: userId,
        approvedAt: new Date(),
        rejectionReason: rejectionReason || null,
      },
    });
  }

  async filterDocumentsByStatus(status: string): Promise<GetDocumentDto[]> {
    return await this.prisma.document.findMany({
      where: { status: status as DocumentStatus },
    });
  }
}
