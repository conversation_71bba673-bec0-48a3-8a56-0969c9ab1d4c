import { ApiExtraModels, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SetMetadata } from '@nestjs/common';
import { ROLES_KEY } from './roles.decorator';
import { PERMISSIONS_KEY } from './permissions.decorator';

// This decorator will add roles and permissions info to Swagger automatically
export const ApiRolesPermissions = (roles: string[], permissions: string[]) => {
  return (target: any, key?: string, descriptor?: PropertyDescriptor) => {
    // Adding metadata for roles and permissions using SetMetadata
    SetMetadata(ROLES_KEY, roles)(target, key, descriptor);
    SetMetadata(PERMISSIONS_KEY, permissions)(target, key, descriptor);

    // Adding custom Swagger operation details
    ApiOperation({
      summary: `Requires roles: ${roles.join(', ')}, permissions: ${permissions.join(', ')}`,
    })(target, key, descriptor);

    ApiResponse({
      status: 403,
      description: `Forbidden. You need one of these roles: ${roles.join(', ')} with permissions: ${permissions.join(', ')}`,
    })(target, key, descriptor);

    // Add ApiExtraModels if needed for proper Swagger documentation
    // This can be used if you want to expose custom data models related to roles/permissions
    ApiExtraModels()(target, key, descriptor);
  };
};
