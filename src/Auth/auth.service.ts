import {
  Injectable,
  InternalServerErrorException,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { CreateUserDto } from '../users/dto/create-user.dto';

@Injectable()
export class AuthService {
  private readonly logger: Logger;

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  // Login method for users
  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email: loginDto.email },
        include: {
          roles: {
            include: {
              permissions: true,
            },
          },
        },
      });

      if (!user) {
        // UnauthorizedException is correctly thrown for invalid email
        throw new UnauthorizedException('Invalid email or password');
      }

      const isPasswordMatched = await bcrypt.compare(
        loginDto.password,
        user.password,
      );
      if (!isPasswordMatched) {
        // UnauthorizedException is correctly thrown for password mismatch
        throw new UnauthorizedException('Invalid email or password');
      }

      const permissions = user.roles.flatMap((role) =>
        role.permissions.map((perm) => perm.name),
      );

      const token = this.jwtService.sign({
        id: user.id,
        roles: user.roles.map((role) => role.name),
        permissions,
      });

      const userDto = {
        ...user, // Spread other user fields
        roles: user.roles.map((role) => role.name), // Map to an array of role names
        permissions: user.roles.flatMap((role) =>
          role.permissions.map((perm) => perm.name),
        ), // Flatten and map to an array of permission names
      };

      return { token, user: userDto };
    } catch (error) {
      // Log unexpected errors and throw InternalServerErrorException
      if (!(error instanceof UnauthorizedException)) {
        // this.logger.error(error);
        throw new InternalServerErrorException('Something went wrong.');
      }
      // Re-throw UnauthorizedException for authorization failures
      throw error;
    }
  }

  register(createUserDto: CreateUserDto) {
    try {
      // Hash the password
      const hashedPassword = bcrypt.hashSync(createUserDto.password, 10);
      // Create the user
      return this.prisma.user.create({
        data: {
          ...createUserDto,
          password: hashedPassword,
        },
      });
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Something went wrong.');
    }
  }
}
