import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { XRequest } from '../XRequest';
import { XConfigService } from '../../common/services/xconfig.service';
import { Socket } from 'socket.io';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import * as jwt from 'jsonwebtoken';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    protected reflector: Reflector,
    protected configService: XConfigService,
  ) {}

  public static async isAuthenticated(request: XRequest) {
    const token = AuthGuard.extractTokenFromHeader(request);
    if (!token) {
      return false;
    }
    try {
      const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
      request.token = decodedToken as any;
    } catch (error) {
      console.error('JWT verification failed:', error);
      return false;
    }
    return true;
  }

  private static extractTokenFromHeader(request: XRequest): string | undefined {
    const headers =
      request.headers ?? (request as unknown as Socket).handshake.headers;
    const [type, token] = headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!this.configService.isAuthEnabled) return true;
    if (isPublic) return true;
    const request = context.switchToHttp().getRequest<XRequest>();
    if (!(await AuthGuard.isAuthenticated(request)))
      throw new UnauthorizedException();

    return true;
  }
}
