import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { PrismaService } from '../prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import {
  InternalServerErrorException,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';

jest.mock('bcrypt', () => ({
  compare: jest.fn(),
}));

// Mocking PrismaService
const mockPrismaService = {
  user: {
    findUnique: jest.fn(),
  },
};

jest.mock('../prisma/prisma.service', () => ({
  PrismaService: jest.fn().mockImplementation(() => mockPrismaService),
}));
jest.mock('@nestjs/jwt');

describe('AuthService', () => {
  let authService: AuthService;
  let prismaService: PrismaService;
  let jwtService: JwtService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        PrismaService,
        // Mock the Logger service
        {
          provide: Logger,
          useValue: { error: jest.fn() },
        },
        JwtService,
      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    prismaService = module.get<PrismaService>(PrismaService);
    jwtService = module.get<JwtService>(JwtService);
  });

  it('should be defined', () => {
    expect(authService).toBeDefined();
  });

  describe('login', () => {
    it('should throw an UnauthorizedException if user is not found', async () => {
      prismaService.user.findUnique = jest.fn().mockResolvedValue(null);

      await expect(
        authService.login({ email: '<EMAIL>', password: 'password' }),
      ).rejects.toThrowError(UnauthorizedException);
    });

    it('should throw an UnauthorizedException if password is incorrect', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        password: 'hashedPassword',
        roles: [],
      };
      prismaService.user.findUnique = jest.fn().mockResolvedValue(mockUser);

      // Mock bcrypt.compare to return false (password mismatch)
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      await expect(
        authService.login({ email: '<EMAIL>', password: 'password' }),
      ).rejects.toThrowError(UnauthorizedException);
    });

    it('should return a token and user data if login is successful', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        password: 'hashedPassword',
        roles: [
          { name: 'admin', permissions: [{ name: 'read' }, { name: 'write' }] },
        ],
      };

      prismaService.user.findUnique = jest.fn().mockResolvedValue(mockUser);

      // Mock bcrypt.compare to return true (password matches)
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      jwtService.sign = jest.fn().mockReturnValue('mockToken');

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password',
      });

      expect(result).toEqual({
        token: 'mockToken',
        user: {
          ...mockUser,
          roles: ['admin'],
          permissions: ['read', 'write'],
        },
      });
      expect(jwtService.sign).toHaveBeenCalledWith({
        id: mockUser.id,
        roles: ['admin'],
        permissions: ['read', 'write'],
      });
    });

    it('should throw InternalServerErrorException on unknown errors', async () => {
      prismaService.user.findUnique = jest
        .fn()
        .mockRejectedValue(new Error('Unexpected error'));

      await expect(
        authService.login({ email: '<EMAIL>', password: 'password' }),
      ).rejects.toThrowError(InternalServerErrorException);
    });
  });
});
