import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { EventType, Prisma } from '@prisma/client';
import { EventDto } from './dto/event.dto';
import { EventQueryParamsDto } from './dto/query-params.dto';

@Injectable()
export class EventLoggerService {
  constructor(private readonly prisma: PrismaService) {}

  async logEvent(eventData: EventDto): Promise<void> {
    try {
      const actor = await this.prisma.actor.create({ data: eventData.actor });
      const context = await this.prisma.context.create({ data: eventData.context });
      const targetEntity = await this.prisma.targetEntity.create({ data: eventData.targetEntity });

      await this.prisma.event.create({
        data: {
          eventType: eventData.eventType,
          type: eventData.targetEntity.type,
          entityId: eventData.targetEntity.entityId,
          fieldModified: eventData.targetEntity.fieldModified,
          actorId: actor.id,
          contextId: context.id,
          targetEntityId: targetEntity.id,
          beforeSnapshot: eventData.beforeSnapshot,
          afterSnapshot: eventData.afterSnapshot,
          sessionId: context.sessionId,
          browserUserAgent: context.browserUserAgent,
          geolocation: context.geolocation,
          parentEventId: eventData.parentEventId ?? null,
        },
      });
    } catch (error) {
      console.error('Failed to log event:', error);
      throw new InternalServerErrorException('Failed to log event');
    }
  }

  async logStudentEvent(
    eventType: EventType,
    studentId: string,
    user: { userId: string; role: string },
    fieldModified: string[],
    beforeSnapshot: any,
    afterSnapshot: any,
  ): Promise<void> {
    try {
      const actor = await this.prisma.actor.create({
        data: {
          userId: user.userId,
          role: user.role,
          ipAddress: '127.0.0.1', // replace later
          deviceId: 'web',        // replace later
        },
      });

      const context = await this.prisma.context.create({
        data: {
          sessionId: 'session-abc', // replace later
          browserUserAgent: 'Chrome/120', // replace later
          geolocation: '0,0', // replace later
        },
      });

      const targetEntity = await this.prisma.targetEntity.create({
        data: {
          type: 'STUDENT',
          entityId: studentId,
          fieldModified,
        },
      });

      await this.prisma.event.create({
        data: {
          eventType,
          type: 'STUDENT',
          entityId: studentId,
          fieldModified,
          actorId: actor.id,
          contextId: context.id,
          targetEntityId: targetEntity.id,
          beforeSnapshot,
          afterSnapshot,
          sessionId: context.sessionId,
          browserUserAgent: context.browserUserAgent,
          geolocation: context.geolocation,
        },
      });
    } catch (error) {
      console.error('Failed to log event:', error);
      throw new InternalServerErrorException('Failed to log student event');
    }
  }

  async getEvents(queryParams: EventQueryParamsDto) {
    const {
      page = 1,
      limit = 10,
      sort = 'desc',
      type,
      entityType,
      startDate,
      endDate,
    } = queryParams;

    const skip = (page - 1) * limit;

    const where: Prisma.EventWhereInput = {
      AND: [
        type ? { eventType: type } : {},
        entityType ? { type: entityType } : {},
        startDate ? { timestamp: { gte: new Date(startDate) } } : {},
        endDate ? { timestamp: { lte: new Date(endDate) } } : {},
      ],
    };

    const [total, events] = await Promise.all([
      this.prisma.event.count({ where }),
      this.prisma.event.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          timestamp: sort,
        },
        include: {
          actor: true,
          targetEntity: true,
          context: true,
        },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: events,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async getStudentEvents(studentId: string, queryParams: EventQueryParamsDto) {
    // First check if student exists
    const student = await this.prisma.student.findUnique({
      where: { id: studentId }
    });

    if (!student) {
      throw new NotFoundException(`Student with ID ${studentId} not found`);
    }

    const {
      page = 1,
      limit = 10,
      sort = 'desc',
      type,
      startDate,
      endDate,
    } = queryParams;

    const skip = (page - 1) * limit;

    const where: Prisma.EventWhereInput = {
      AND: [
        { type: 'STUDENT', entityId: studentId },
        type ? { eventType: type } : {},
        startDate ? { timestamp: { gte: new Date(startDate) } } : {},
        endDate ? { timestamp: { lte: new Date(endDate) } } : {},
      ],
    };

    const [total, events] = await Promise.all([
      this.prisma.event.count({ where }),
      this.prisma.event.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          timestamp: sort,
        },
        include: {
          actor: true,
          targetEntity: true,
          context: true,
        },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: events,
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}
