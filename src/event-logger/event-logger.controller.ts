import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { EventLoggerService } from './event-logger.service';
import { EventQueryParamsDto } from './dto/query-params.dto';
import { Permissions } from '../Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from '../Auth/decorators/roles-permissions.swagger.decorator';

@ApiBearerAuth()
@ApiTags('Events')
@Controller('events')
export class EventLoggerController {
  constructor(private readonly eventLoggerService: EventLoggerService) {}

  @Get()
  @ApiOperation({ summary: 'Get all events with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Returns events with pagination metadata' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Permissions('READ')
  @ApiRolesPermissions(['ADMIN', 'COUNSELLOR'], ['READ'])
  async getEvents(@Query() queryParams: EventQueryParamsDto) {
    return this.eventLoggerService.getEvents(queryParams);
  }

  @Get('student/:studentId')
  @ApiOperation({ summary: 'Get all events for a specific student' })
  @ApiResponse({ status: 200, description: 'Returns student events with pagination metadata' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Permissions('READ')
  @ApiRolesPermissions(['ADMIN', 'COUNSELLOR'], ['READ'])
  async getStudentEvents(
    @Param('studentId') studentId: string,
    @Query() queryParams: EventQueryParamsDto,
  ) {
    return this.eventLoggerService.getStudentEvents(studentId, queryParams);
  }
} 