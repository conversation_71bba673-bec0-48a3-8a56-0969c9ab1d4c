import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';


@ApiTags('Permissions')
@ApiBearerAuth()
@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Post()
  create(@Body() createPermissionDto: CreatePermissionDto) {
    return this.permissionsService.create(createPermissionDto);
  }

  @Post('assign')
  assignPermissionsToRole(
    @Body('roleId') roleId: string,
    @Body('permissionIds') permissionIds: string[],
  ) {
    return this.permissionsService.assignPermissionsToRole(
      roleId,
      permissionIds,
    );
  }

  @Get()
  findAll(@Query() pageOptions: PageOptionsDto) {
    return this.permissionsService.findAll(pageOptions);
  }

  @Get(':name')
  findOne(@Param('name') name: string) {
    return this.permissionsService.findOne(name);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ) {
    return this.permissionsService.update(id, updatePermissionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.permissionsService.remove(id);
  }
}
