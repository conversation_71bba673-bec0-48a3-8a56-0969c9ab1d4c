import { ApiProperty } from '@nestjs/swagger';
import { Permission } from '@prisma/client';

export class PermissionDto implements Permission {
  @ApiProperty({
    description: 'The unique identifier of the permission',
    type: String,
  })
  id: string;

  @ApiProperty({ description: 'The name of the permission', type: String })
  name: string;

  @ApiProperty({
    description: 'A description of the permission',
    type: String,
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'A group of the permission',
    type: String,
    nullable: true,
  })
  group: string | null;
}
