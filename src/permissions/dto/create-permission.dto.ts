import { Permission } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CreatePermissionDto implements Omit<Permission, 'id'> {
  @ApiProperty({
    example: 'CREATE | READ | UPDATE | DELETE',
    description: 'The name of the permission',
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 'Create a new users',
    description: 'The description of the permission',
  })
  @IsString()
  description: string;

  @ApiProperty({
    example: 'Employee | Manager | Admin',
    description: 'The group of the permission',
  })
  @IsString()
  group: string;
}
