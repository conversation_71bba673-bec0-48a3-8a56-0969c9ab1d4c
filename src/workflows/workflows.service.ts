import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConditionType, StepType } from '@prisma/client';
import { getDMMF } from '@prisma/sdk';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { PrismaService } from 'src/prisma/prisma.service';
import * as file from 'src/workflows/actions.json';
import { CreateStepDto } from './dto/create-step.dto';
import { CreateWorkflowDto } from './dto/create-workflow.dto';
import { UpdateWorkflowDto } from './dto/update-workflow.dto';
import { WorkflowDto } from './dto/workflow.dto';

@Injectable()
export class WorkflowsService {
  constructor(
    private readonly prisma: PrismaService,
    // private prismaClient: PrismaClient,
  ) {}

  evaluateCondition(condition: any): any {
    if (condition.type === ConditionType.ARITHMETIC) {
      const left = condition.leftValue.value;
      const right = condition.rightValue.value;
      const op = condition.operator;
      let result;
      switch (op) {
        case '+':
          result = left + right;
          break;
        case '-':
          result = left - right;
          break;
        case '*':
          result = left * right;
          break;
        case '/':
          result = left / right;
          break;
        default:
          console.warn('Unsupported arithmetic operator:', op);
          result = null;
      }
      return result;
    } else if (
      condition.type === ConditionType.COMPARISON ||
      condition.type === ConditionType.CONDITION
    ) {
      const left = condition.leftValue.value;
      const right = condition.rightValue.value;
      const op = condition.operator;
      switch (op) {
        case '>':
          return left > right;
        case '<':
          return left < right;
        case '==':
          return left === right;
        default:
          console.warn('Unsupported comparison operator:', op);
          return false;
      }
    }
    return null;
  }

  async executeStepRecursive(step: any, context: any): Promise<any> {
    console.log(`Executing step: "${step.name}" with context:`, context);
    let currentContext = context;

    // Check if the step has a condition attached.
    const condition = step.ifCondition || step.elseCondition;
    if (condition) {
      const evalResult = this.evaluateCondition(condition);
      console.log(`Step "${step.name}" condition evaluated to:`, evalResult);

      if (condition.type === ConditionType.ARITHMETIC) {
        // For arithmetic, we pass on the computed numeric result.
        currentContext = evalResult;
        if (condition.ifBranch && condition.ifBranch.length > 0) {
          for (const branchStep of condition.ifBranch) {
            currentContext = await this.executeStepRecursive(
              branchStep,
              currentContext,
            );
          }
        }
      } else if (
        condition.type === ConditionType.COMPARISON ||
        condition.type === ConditionType.CONDITION
      ) {
        if (evalResult) {
          // Execute IF branch if condition is true.
          if (condition.ifBranch && condition.ifBranch.length > 0) {
            for (const branchStep of condition.ifBranch) {
              currentContext = await this.executeStepRecursive(
                branchStep,
                currentContext,
              );
            }
          }
        } else {
          // Execute ELSE branch if condition is false.
          if (condition.elseBranch && condition.elseBranch.length > 0) {
            for (const branchStep of condition.elseBranch) {
              currentContext = await this.executeStepRecursive(
                branchStep,
                currentContext,
              );
            }
          }
        }
      }
    } else {
      // No condition: execute the step directly.
      if (step.type === 'action') {
        console.log('Performing action:', step.details);
        currentContext = { ...currentContext, lastAction: step.name };
      } else if (step.type === 'calculation') {
        console.log('Performing calculation:', step.details);
        // You could extend this to compute values based on step.details.
      }
    }

    return currentContext;
  }

  async executeWorkflowTree(workflowId: string) {
    const workflow = await this.prisma.workflow.findUnique({
      where: { id: workflowId },
      include: {
        steps: {
          include: {
            ifCondition: {
              include: {
                ifBranch: true,
                elseBranch: true,
              },
            },
            elseCondition: true,
            action: true,
          },
          orderBy: { order: 'asc' },
        },
      },
    });
    if (!workflow) {
      console.error('Workflow not found');
      return;
    }

    console.log('Executing workflow tree:', workflow.name);

    let context: any = {};
    for (const step of workflow.steps) {
      context = await this.executeStepRecursive(step, context);
    }

    console.log('Final execution context:', context);
  }

  /**
   * Recursively creates steps and their nested branch steps.
   *
   * @param steps Array of steps to create.
   * @param workflowId The ID of the workflow these steps belong to.
   * @param prisma Instance of PrismaService.
   * @param parentId Optional parent step ID (or condition ID for branch steps).
   */
  async createStepsRecursively(
    steps: CreateStepDto[],
    workflowId: string,
    parentStepId?: string,
  ): Promise<void> {
    let order = 0;
    for (const step of steps) {
      let stepData: any = {
        name: step.name,
        type: step.type,
        order: step.order || order++,
        workflow_id: workflowId,
        details: step.details,
      };

      if (step.type === StepType.CALCULATION) {
        if (step.condition) {
          const condition = await this.prisma.condition.create({
            data: {
              type: step.condition.type,
              leftValue: step.condition.leftValue,
              rightValue: step.condition.rightValue,
              operator: step.condition.operator,
              criteria: step.condition.criteria,
            },
          });

          stepData.conditionId = condition.id;

          if (step.condition.ifBranch && step.condition.ifBranch.length > 0) {
            await this.createStepsRecursively(
              step.condition.ifBranch,
              workflowId,
              condition.id,
            );
          }

          if (
            step.condition.elseBranch &&
            step.condition.elseBranch.length > 0
          ) {
            await this.createStepsRecursively(
              step.condition.elseBranch,
              workflowId,
              condition.id,
            );
          }
        }
      } else if (step.action) {
        const action = await this.prisma.action.create({
          data: {
            type: step.action.type,
            details: step.action.details,
          },
        });

        stepData.actionId = action.id;
      }

      const createdStep = await this.prisma.step.create({
        data: stepData,
      });

      if (step.condition?.ifBranch) {
        await this.createStepsRecursively(
          step.condition.ifBranch,
          workflowId,
          createdStep.id,
        );
      }
      if (step.condition?.elseBranch) {
        await this.createStepsRecursively(
          step.condition.elseBranch,
          workflowId,
          createdStep.id,
        );
      }
    }
  }

  async createWorkflow(
    createWorkflowDto: CreateWorkflowDto,
  ): Promise<WorkflowDto> {
    try {
      const { steps, triggers, ...workflowData } = createWorkflowDto;

      return this.prisma.$transaction(async (prisma) => {
        const workflow = await this.prisma.workflow.create({
          data: workflowData,
        });

        if (steps && steps.length > 0) {
          await this.createStepsRecursively(steps, workflow.id);
        }

        if (triggers && triggers.length > 0) {
          await this.prisma.trigger.createMany({
            data: triggers.map((trigger) => ({
              workflow_id: workflow.id,
              ...trigger,
            })),
          });
        }

        return await this.prisma.workflow.findUnique({
          where: { id: workflow.id },
          select: {
            id: true,
            name: true,
            description: true,
            active: true,
            steps: true,
            triggers: true,
          },
        });
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllWorkflows(): Promise<WorkflowDto[]> {
    try {
      const workflows = await this.prisma.workflow.findMany({
        select: {
          id: true,
          name: true,
          description: true,
          active: true,
          steps: true,
          triggers: true,
        },
      });

      return workflows;
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getWorkflowById(id: string): Promise<WorkflowDto> {
    try {
      const workflow = await this.prisma.workflow.findUnique({
        where: {
          id: id,
        },
        select: {
          id: true,
          name: true,
          description: true,
          active: true,
          steps: true,
          triggers: true,
        },
      });

      if (!workflow) {
        throw new NotFoundException(`Workflow with id ${id} not found`);
      }

      return workflow;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateWorkflow(
    id: string,
    updateWorkflow: UpdateWorkflowDto,
  ): Promise<WorkflowDto> {
    try {
      const { steps, triggers, ...workflowData } = updateWorkflow;

      return this.prisma.$transaction(async (prisma) => {
        await prisma.workflow.update({
          where: { id },
          data: workflowData,
        });

        if (steps) {
          await prisma.step.deleteMany({
            where: { workflow_id: id },
          });

          await prisma.step.createMany({
            data: steps.map((step, index) => ({
              workflow_id: id,
              order: index,
              ...step,
            })),
          });
        }

        if (triggers) {
          await prisma.trigger.deleteMany({
            where: { workflow_id: id },
          });

          await prisma.trigger.createMany({
            data: triggers.map((trigger) => ({
              workflow_id: id,
              ...trigger,
            })),
          });
        }

        return prisma.workflow.findUnique({
          where: { id },
          select: {
            id: true,
            name: true,
            description: true,
            active: true,
            steps: true,
            triggers: true,
          },
        });
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async toggleWorkflow(id: string): Promise<WorkflowDto> {
    try {
      const workflow = await this.prisma.workflow.findUnique({
        where: {
          id: id,
        },
      });

      if (!workflow) {
        throw new NotFoundException(`Workflow with id ${id} not found`);
      }

      return this.prisma.workflow.update({
        where: { id },
        data: { active: !workflow.active },
        select: {
          id: true,
          name: true,
          description: true,
          active: true,
          steps: true,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getSchemaStructure(): Promise<any> {
    try {
      // Read your Prisma schema file (adjust the path as needed)
      const schemaPath = join(process.cwd(), 'prisma', 'schema.prisma');
      const datamodel = await readFile(schemaPath, 'utf8');

      // Get the DMMF using the public API
      const dmmf = await getDMMF({ datamodel });

      const models = dmmf.datamodel.models.map((model) => ({
        name: model.name,
        fields: model.fields.map((field) => ({
          name: field.name,
          type: field.type,
          isRequired: field.isRequired,
          isList: field.isList,
          kind: field.kind,
          relationName: field.relationName,
          enum:
            field.kind === 'enum' ? this.getEnumValues(field.type, dmmf) : null,
        })),
      }));
      const enums = dmmf.datamodel.enums.map((enumItem) => ({
        name: enumItem.name,
        values: enumItem.values,
      }));

      return {
        models,
        enums,
      };
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private getEnumValues(enumName: string, dmmf: any) {
    const enumDef = dmmf.datamodel.enums.find((e: any) => e.name === enumName);
    return enumDef?.values || [];
  }

  async getActionsDetails(): Promise<any> {
    try {
      // const filePath = join(__dirname, 'actions.json');
      // console.log(filePath);
      // console.log(file);

      // const fileContents = await readFile(filePath, 'utf8');

      // const actions = JSON.parse(fileContents);
      // const actions = JSON.parse(file);

      return file;
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // async handleEvent(eventType: EventType, eventData: any, actor?: Actor) {
  //   const triggers = await this.prisma.trigger.findMany({
  //     where: { event_type: eventType, actor: actor },
  //     include: {
  //       workflow: {
  //         include: { steps: { include: { condition: true, action: true } } },
  //       },
  //     },
  //   });

  //   for (const trigger of triggers) {
  //     const workflow = trigger.workflow;
  //     if (!workflow.active) continue;

  //     await this.executeStepsRecursively(workflow.steps, eventData);
  //   }
  // }

  // async executeStepsRecursively(steps, eventData) {
  //   for (const step of steps) {
  //     if (step.condition) {
  //       const result = this.evaluateCondition(
  //         step.condition.criteria,
  //         eventData,
  //       );
  //       if (!result) continue; // Skip this step if condition is not met
  //     }

  //     if (step.action) {
  //       await this.executeAction(
  //         step.action.name,
  //         step.action.arguments,
  //         eventData,
  //       );
  //     }

  //     if (step.steps && step.steps.length > 0) {
  //       await this.executeStepsRecursively(step.steps, eventData);
  //     }
  //   }
  // }

  // evaluateCondition(criteria: any, eventData: any): boolean {
  //   // Implement condition evaluation logic based on criteria
  //   if (criteria.checkActor && criteria.checkActor === eventData.actor) {
  //     return true;
  //   }
  //   // Add more condition checks as needed
  //   return false;
  // }

  // async executeAction(name: string, args: any, eventData: any) {
  //   // Implement action execution logic based on the function name
  //   switch (name) {
  //     case 'sendEmail':
  //       // Call email service with arguments
  //       console.log(
  //         `Sending email with arguments: ${JSON.stringify(arguments)}`,
  //       );
  //       break;
  //     case 'updateEntity':
  //       // Update entity in the database using arguments
  //       console.log(
  //         `Updating entity with arguments: ${JSON.stringify(arguments)}`,
  //       );
  //       break;
  //     // Add more action types as needed
  //     default:
  //       console.log(`Action "${name}" not implemented.`);
  //   }
  // }
}
