import { Test, TestingModule } from '@nestjs/testing';
import { WorkflowsService } from './workflows.service';
import { PrismaService } from '../prisma/prisma.service';

describe('WorkflowsService', () => {
  let service: WorkflowsService;

  const mockPrismaService = {
    workflowStep: {
      createMany: jest.fn(),
    },
    workflow: {
      findUnique: jest.fn().mockResolvedValue({
        id: 'workflow-1',
        steps: [],
      }),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<WorkflowsService>(WorkflowsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create steps without condition safely', async () => {
    const steps = [
      {
        id: 'step1',
        action: 'action1',
        ifBranch: [],
        elseBranch: [],
      },
    ];

    await service['flattenSteps'](steps);

    expect(mockPrismaService.workflowStep.createMany).toHaveBeenCalledWith({
      data: expect.arrayContaining([
        expect.objectContaining({
          id: 'step1',
          action: 'action1',
        }),
      ]),
    });
  });

  it('should handle steps with nested ifBranch and elseBranch', async () => {
    const steps = [
      {
        id: 'step1',
        action: 'action1',
        ifBranch: [
          {
            id: 'step2',
            action: 'action2',
            ifBranch: [],
            elseBranch: [],
          },
        ],
        elseBranch: [
          {
            id: 'step3',
            action: 'action3',
            ifBranch: [],
            elseBranch: [],
          },
        ],
      },
    ];

    await service['flattenSteps'](steps);

    expect(mockPrismaService.workflowStep.createMany).toHaveBeenCalledWith({
      data: expect.arrayContaining([
        expect.objectContaining({ id: 'step1' }),
        expect.objectContaining({ id: 'step2' }),
        expect.objectContaining({ id: 'step3' }),
      ]),
    });
  });

  it('should handle deeply nested steps recursively', async () => {
    const steps = [
      {
        id: 'step1',
        action: 'action1',
        ifBranch: [
          {
            id: 'step2',
            action: 'action2',
            ifBranch: [
              {
                id: 'step3',
                action: 'action3',
                ifBranch: [],
                elseBranch: [],
              },
            ],
            elseBranch: [],
          },
        ],
        elseBranch: [],
      },
    ];

    await service['flattenSteps'](steps);

    expect(mockPrismaService.workflowStep.createMany).toHaveBeenCalledWith({
      data: expect.arrayContaining([
        expect.objectContaining({ id: 'step1' }),
        expect.objectContaining({ id: 'step2' }),
        expect.objectContaining({ id: 'step3' }),
      ]),
    });
  });

  it('should handle step with missing action gracefully', async () => {
    const steps = [
      {
        id: 'step1',
        ifBranch: [],
        elseBranch: [],
      },
    ];

    await service['flattenSteps'](steps);

    expect(mockPrismaService.workflowStep.createMany).toHaveBeenCalledWith({
      data: expect.arrayContaining([
        expect.objectContaining({ id: 'step1', action: undefined }),
      ]),
    });
  });
});
