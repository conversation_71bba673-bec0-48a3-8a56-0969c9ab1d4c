import { ConflictException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUniversityDto } from './dto/create-university.dto';
import { UpdateUniversityDto } from './dto/update-university.dto';
import { UniversitiesService } from './universities.service';

describe('UniversitiesService', () => {
  let service: UniversitiesService;
  let prisma: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UniversitiesService,
        {
          provide: PrismaService,
          useValue: {
            university: {
              findUnique: jest.fn(),
              create: jest.fn(),
              findMany: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<UniversitiesService>(UniversitiesService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createUniversity', () => {
    it('should create a university successfully', async () => {
      const createUniversityDto: CreateUniversityDto = {
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
      };
      const result = {
        id: '1',
        name: createUniversityDto.name,
        description: createUniversityDto.description,
        location: createUniversityDto.location,
        ranking: createUniversityDto.ranking,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue(null);
      jest.spyOn(prisma.university, 'create').mockResolvedValue(result);

      expect(await service.createUniversity(createUniversityDto)).toEqual({
        id: result.id,
        name: result.name,
        description: result.description,
        location: result.location,
        ranking: result.ranking,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      });
    });

    it('should throw ConflictException if university already exists', async () => {
      const createUniversityDto: CreateUniversityDto = {
        name: 'Existing University',
        description: 'Existing Description',
        location: 'Existing Location',
        ranking: 1,
      };
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue({
        id: '1',
        name: createUniversityDto.name,
        description: createUniversityDto.description,
        location: createUniversityDto.location,
        ranking: createUniversityDto.ranking,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await expect(
        service.createUniversity(createUniversityDto),
      ).rejects.toThrow(ConflictException);
    });

    it('should throw PrismaClientKnownRequestError if database error occurs', async () => {
      const createUniversityDto: CreateUniversityDto = {
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
      };
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue(null);
      jest.spyOn(prisma.university, 'create').mockRejectedValue(
        new PrismaClientKnownRequestError('Database error', {
          code: 'P2002',
          clientVersion: '1.0.0',
        }),
      );

      await expect(
        service.createUniversity(createUniversityDto),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('getAllUniversities', () => {
    it('should get all universities successfully', async () => {
      const result = [
        {
          id: '1',
          name: 'Test University 1',
          description: 'Test Description 1',
          location: 'Test Location 1',
          ranking: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          name: 'Test University 2',
          description: 'Test Description 2',
          location: 'Test Location 2',
          ranking: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      jest.spyOn(prisma.university, 'findMany').mockResolvedValue(result);

      expect(await service.getAllUniversities()).toEqual(
        result.map((university) => ({
          id: university.id,
          name: university.name,
          description: university.description,
          location: university.location,
          ranking: university.ranking,
          createdAt: university.createdAt,
          updatedAt: university.updatedAt,
        })),
      );
    });

    it('should return an empty array if no universities exist', async () => {
      jest.spyOn(prisma.university, 'findMany').mockResolvedValue([]);

      expect(await service.getAllUniversities()).toEqual([]);
    });
  });

  describe('getUniversityById', () => {
    it('should get a university by ID successfully', async () => {
      const result = {
        id: '1',
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue(result);

      expect(await service.getUniversityById('1')).toEqual({
        id: result.id,
        name: result.name,
        description: result.description,
        location: result.location,
        ranking: result.ranking,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      });
    });

    it('should throw NotFoundException if university not found', async () => {
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue(null);

      await expect(service.getUniversityById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateUniversity', () => {
    it('should update a university successfully', async () => {
      const updateUniversityDto: UpdateUniversityDto = {
        name: 'Updated University',
        description: 'Updated Description',
        location: 'Updated Location',
        ranking: 2,
      };
      const result = {
        id: '1',
        name: updateUniversityDto.name!,
        description: updateUniversityDto.description!,
        location: updateUniversityDto.location!,
        ranking: updateUniversityDto.ranking!,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue({
        id: '1',
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      jest.spyOn(prisma.university, 'update').mockResolvedValue(result);

      expect(await service.updateUniversity('1', updateUniversityDto)).toEqual({
        id: result.id,
        name: result.name,
        description: result.description,
        location: result.location,
        ranking: result.ranking,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      });
    });

    it('should throw NotFoundException if university not found', async () => {
      const updateUniversityDto: UpdateUniversityDto = {
        name: 'Updated University',
      };
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue(null);

      await expect(
        service.updateUniversity('999', updateUniversityDto),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteUniversity', () => {
    it('should delete a university successfully', async () => {
      let result = {
        id: '1',
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue({
        id: '1',
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      jest.spyOn(prisma.university, 'delete').mockResolvedValue(undefined);

      expect(await service.deleteUniversity('1')).toEqual({
        id: result.id,
        name: result.name,
        description: result.description,
        location: result.location,
        ranking: result.ranking,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      });
    });

    it('should throw NotFoundException if university not found', async () => {
      jest.spyOn(prisma.university, 'findUnique').mockResolvedValue(null);

      await expect(service.deleteUniversity('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
