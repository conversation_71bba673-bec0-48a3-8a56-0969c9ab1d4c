import { <PERSON><PERSON><PERSON>, IsEnum, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { User, UserStatus } from '@prisma/client';

export class CreateUserDto
  implements Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'uid'>
{
  @ApiProperty({ description: 'The name of the user' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'The email of the user' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'The password of the user', minLength: 6 })
  @IsString()
  password: string;

  @ApiProperty({
    description: 'The status of the User',
    enum: UserStatus,
    default: UserStatus.INACTIVE,
  })
  @IsEnum(UserStatus)
  status: UserStatus;
}
