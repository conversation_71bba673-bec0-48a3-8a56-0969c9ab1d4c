import { Injectable } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PrismaService } from '../prisma/prisma.service';
import { PageOptionsDto } from '../common/dtos/pageOptions.dto';
import { PageMetaDto } from '../common/dtos/pageMeta.dto';
import { PageDto } from '../common/dtos/page.dto';
import { User } from '@prisma/client';

@Injectable()
export class UsersService {
  constructor(private readonly prisma: PrismaService) {}

  create(createUserDto: CreateUserDto) {
    // todo: create a new user in auto task and add the uid to the user
    return this.prisma.user.create({
      data: {
        ...createUserDto,
      },
    });
  }

  async findAll(pageOptionsDto: PageOptionsDto) {
    const { skip, take = 10 } = pageOptionsDto;

    const [data, itemCount] = await Promise.all([
      this.prisma.user.findMany({
        take: take,
        skip: skip,
        include: {
          roles: {
            include: {
              permissions: true,
            },
          },
        },
      }),
      this.prisma.user.count(),
    ]);

    const meta = new PageMetaDto({ itemCount, pageOptionsDto });
    return new PageDto<User>(data, meta);
  }

  async findOne(id: string): Promise<User> {
    return this.prisma.user.findUnique({
      where: { id },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    return this.prisma.user.update({
      where: { id },
      data: {
        ...updateUserDto,
      },
    });
  }

  async remove(id: string) {
    return this.prisma.user.delete({
      where: { id },
    });
  }
}
