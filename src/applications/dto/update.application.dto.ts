import { ApiProperty } from '@nestjs/swagger';
import { ApplicationStatus, ApplicationSubStatus, ApplicantStatus} from '@prisma/client';
import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';


export class UpdateApplicationDto {
  @ApiProperty({
    description: 'The status of the Application',
    enum: ApplicationStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @IsOptional()
  @IsEnum(ApplicantStatus)
  @ApiProperty({
  description: 'Updated status representing action taken by applicant',
  enum: ApplicantStatus,
  required: false,
})
  applicantStatus?: ApplicantStatus;

  @ApiProperty({
    description: 'The sub-status of the Application',
    enum: ApplicationSubStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ApplicationSubStatus)
  subStatus?: ApplicationSubStatus;

  @ApiProperty({
    description: 'The date when the Application was submitted',
    type: Date,
    required: false,
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  appliedDate?: Date;

  @ApiProperty({
    description: 'The date when a decision was made on the Application',
    type: Date,
    required: false,
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  decisionDate?: Date;

  @ApiProperty({
    description: 'The date when visa was applied for',
    type: Date,
    required: false,
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  visaAppliedDate?: Date;

  @ApiProperty({
    description: 'The date when a decision was made on the visa Application',
    type: Date,
    required: false,
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  visaDecisionDate?: Date;

  @ApiProperty({
    description: 'Any additional notes',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
