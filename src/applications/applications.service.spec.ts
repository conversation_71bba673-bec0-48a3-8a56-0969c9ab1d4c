import { HttpException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationStatus, ApplicationSubStatus, ApplicantStatus } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';
import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update.application.dto';

describe('ApplicationsService', () => {
  let service: ApplicationsService;
  let prisma: PrismaService;

  const baseApp = {
    id: '1',
    studentId: 'student-1',
    universityId: 'university-1',
    programId: 'program-1',
    status: ApplicationStatus.ACCEPTED,
    subStatus: ApplicationSubStatus.FOLLOW_UP,
    applicantStatus: ApplicantStatus.OFFER_ACCEPTED,
    appliedDate: new Date(),
    decisionDate: new Date(),
    visaAppliedDate: new Date(),
    visaDecisionDate: new Date(),
    notes: 'Test note',
    createdAt: new Date(),
    updatedAt: new Date(),
    documents: [],
    activityLogs: [],
    student: null,
    university: null,
    program: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationsService,
        {
          provide: PrismaService,
          useValue: {
            application: {
              findUnique: jest.fn(),
              create: jest.fn(),
              findMany: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<ApplicationsService>(ApplicationsService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createApplication', () => {
    it('should create an application successfully', async () => {
      const dto: CreateApplicationDto = {
        studentId: baseApp.studentId,
        universityId: baseApp.universityId,
        programId: baseApp.programId,
        status: baseApp.status,
        subStatus: baseApp.subStatus,
        applicantStatus: baseApp.applicantStatus,
        appliedDate: baseApp.appliedDate,
        decisionDate: baseApp.decisionDate,
        visaAppliedDate: baseApp.visaAppliedDate,
        visaDecisionDate: baseApp.visaDecisionDate,
        notes: baseApp.notes,
      };

      jest.spyOn(prisma.application, 'create').mockResolvedValue(baseApp);

      expect(await service.createApplication(dto)).toEqual(baseApp);
    });
  });

  describe('getApplicationById', () => {
    it('should return an application', async () => {
      jest.spyOn(prisma.application, 'findUnique').mockResolvedValue(baseApp);

      expect(await service.getApplicationById(baseApp.id)).toEqual(baseApp);
    });

    it('should throw NotFoundException if not found', async () => {
      jest.spyOn(prisma.application, 'findUnique').mockResolvedValue(null);

      await expect(service.getApplicationById('invalid-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateApplication', () => {
    it('should update and return the application', async () => {
      const updated = { ...baseApp, status: ApplicationStatus.ACCEPTED };

      jest.spyOn(prisma.application, 'findUnique').mockResolvedValue(baseApp);
      jest.spyOn(prisma.application, 'update').mockResolvedValue(updated);

      const dto: UpdateApplicationDto = { status: ApplicationStatus.ACCEPTED };

      expect(await service.updateApplication(baseApp.id, dto)).toEqual(updated);
    });
  });

  describe('deleteApplication', () => {
    it('should delete and return the application', async () => {
      jest.spyOn(prisma.application, 'findUnique').mockResolvedValue(baseApp);
      jest.spyOn(prisma.application, 'delete').mockResolvedValue(baseApp);

      expect(await service.deleteApplication(baseApp.id)).toEqual(baseApp);
    });
  });
});
