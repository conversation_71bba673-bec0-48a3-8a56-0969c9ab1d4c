services:
  backend:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    container_name: fes_crm_prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      BACKEND_PORT: 5000
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES: ${JWT_EXPIRES}
      ENABLE_AUTH: ${ENABLE_AUTH}
    ports:
      - '5000:5000'
    volumes:
      - ./uploads:/usr/src/app/uploads
    networks:
      - fes_crm_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  fes_crm_network:
    driver: bridge

volumes:
  uploads:
