name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Cache node_modules
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Install dependencies
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: SSH Remove Existing Files Except .env
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/fes-crm-backend
            # Remove all files except .env
            find . ! -name '.env' -type f -delete
            find . ! -name '.env' -type d -empty -delete

      - name: Copy files to Production Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          source: '.'
          target: '/home/<USER>/fes-crm-backend'

      - name: SSH Set File Permissions
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/fes-crm-backend
            find . -type d -exec chmod 755 {} \;
            find . -type f -exec chmod 644 {} \;
            sudo chmod +x entrypoint.sh
            sudo chown -R $USER:$USER ~/fes-crm-backend

      - name: SSH Deploy to Production
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/fes-crm-backend
            # Install dependencies with caching
            npm ci
            # Generate Prisma client
            npx prisma generate
            # Build the application
            npm run build
            # Run database migrations
            npx prisma migrate deploy
            # Seed the database
            npm run migrate
            # Restart the application using PM2
            pm2 restart fes-crm-backend || pm2 start npm --name "fes-crm-backend" -- run start:prod