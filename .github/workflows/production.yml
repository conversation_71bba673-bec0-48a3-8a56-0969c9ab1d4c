name: Zero-Downtime Production Deployment

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean
      rollback_version:
        description: 'Rollback to specific version (commit SHA)'
        required: false
        type: string

env:
  DEPLOYMENT_TIMEOUT: 600
  HEALTH_CHECK_RETRIES: 30
  HEALTH_CHECK_INTERVAL: 10
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

jobs:
  security-scan:
    name: Security & Vulnerability Scan
    runs-on: ubuntu-latest
    outputs:
      security-passed: ${{ steps.security-check.outputs.passed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
          exit-code: '1'
        continue-on-error: true
        id: trivy-scan

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Security check result
        id: security-check
        run: |
          if [ "${{ steps.trivy-scan.outcome }}" == "success" ] || [ "${{ inputs.force_deploy }}" == "true" ]; then
            echo "passed=true" >> $GITHUB_OUTPUT
          else
            echo "passed=false" >> $GITHUB_OUTPUT
          fi

  build-and-test:
    name: Build & Test Application
    runs-on: ubuntu-latest
    needs: security-scan
    if: needs.security-scan.outputs.security-passed == 'true'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm run test -- --coverage

      - name: Build application
        run: npm run build

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: fes-crm-backend
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build Docker image
        id: build
        run: |
          docker build -f Dockerfile.prod -t fes-crm-backend:${{ github.sha }} .
          docker tag fes-crm-backend:${{ github.sha }} fes-crm-backend:latest
          echo "digest=$(docker images --digests fes-crm-backend:${{ github.sha }} --format '{{.Digest}}')" >> $GITHUB_OUTPUT

      - name: Save Docker image
        run: |
          docker save fes-crm-backend:${{ github.sha }} | gzip > fes-crm-backend-${{ github.sha }}.tar.gz

      - name: Upload Docker image artifact
        uses: actions/upload-artifact@v4
        with:
          name: docker-image-${{ github.sha }}
          path: fes-crm-backend-${{ github.sha }}.tar.gz
          retention-days: 7

  deploy:
    name: Zero-Downtime Deployment
    runs-on: ubuntu-latest
    needs: [security-scan, build-and-test]
    if: needs.security-scan.outputs.security-passed == 'true'
    environment:
      name: production
      url: http://${{ secrets.PROD_HOST }}:5000
    outputs:
      deployment-status: ${{ steps.deployment-result.outputs.status }}
      previous-version: ${{ steps.get-current-version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Docker image
        uses: actions/download-artifact@v4
        with:
          name: docker-image-${{ github.sha }}

      - name: Notify deployment start
        if: env.SLACK_WEBHOOK != ''
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"🚀 Starting zero-downtime deployment of FES CRM Backend\nCommit: ${{ github.sha }}\nBranch: ${{ github.ref_name }}\nActor: ${{ github.actor }}"}' \
            ${{ env.SLACK_WEBHOOK }}

      - name: Copy files to production server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          source: |
            docker-compose.prod.yml
            Dockerfile.prod
            entrypoint.prod.sh
            package*.json
            prisma/
            src/
            scripts/
            fes-crm-backend-${{ github.sha }}.tar.gz
          target: /home/<USER>/fes-crm-backend-new
          strip_components: 0

      - name: Get current version
        id: get-current-version
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            if [ -f /home/<USER>/fes-crm-backend/.version ]; then
              echo "version=$(cat /home/<USER>/fes-crm-backend/.version)" >> $GITHUB_OUTPUT
            else
              echo "version=none" >> $GITHUB_OUTPUT
            fi

      - name: Zero-downtime deployment
        id: deploy
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          timeout: ${{ env.DEPLOYMENT_TIMEOUT }}s
          script: |
            set -e

            # Colors for output
            RED='\033[0;31m'
            GREEN='\033[0;32m'
            YELLOW='\033[1;33m'
            NC='\033[0m'

            log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
            warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
            error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }

            # Configuration
            NEW_VERSION="${{ github.sha }}"
            APP_DIR="/home/<USER>/fes-crm-backend"
            NEW_DIR="/home/<USER>/fes-crm-backend-new"
            BACKUP_DIR="/home/<USER>/fes-crm-backend-backup-$(date +%Y%m%d-%H%M%S)"

            log "Starting zero-downtime deployment for version ${NEW_VERSION}"

            # Create deployment directory structure
            mkdir -p "${NEW_DIR}"
            cd "${NEW_DIR}"

            # Load Docker image
            log "Loading new Docker image..."
            sudo docker load < fes-crm-backend-${NEW_VERSION}.tar.gz

            # Copy environment file from current deployment
            if [ -f "${APP_DIR}/.env" ]; then
              cp "${APP_DIR}/.env" "${NEW_DIR}/.env"
              log "Environment file copied from current deployment"
            else
              error "No .env file found in current deployment"
              exit 1
            fi

            # Set permissions
            chmod +x entrypoint.prod.sh
            sudo chown -R $USER:$USER "${NEW_DIR}"

            # Database migration safety check
            log "Checking database migration safety..."
            export $(cat .env | grep -v '^#' | xargs)

            # Create backup of current deployment
            if [ -d "${APP_DIR}" ]; then
              log "Creating backup of current deployment..."
              sudo cp -r "${APP_DIR}" "${BACKUP_DIR}"
            fi

            # Run database migrations in a safe way
            log "Running database migrations..."
            sudo docker run --rm \
              --env-file .env \
              --network host \
              fes-crm-backend:${NEW_VERSION} \
              sh -c "npx prisma migrate deploy --schema=./prisma/schema.prisma"

            if [ $? -ne 0 ]; then
              error "Database migration failed"
              exit 1
            fi

            # Update docker-compose with new image tag
            sed -i "s|fes-crm-backend:.*|fes-crm-backend:${NEW_VERSION}|g" docker-compose.prod.yml

            # Start new containers with blue-green deployment
            log "Starting new containers (green environment)..."

            # Use different port for green environment
            export GREEN_PORT=5001
            sed -i "s|5000:5000|${GREEN_PORT}:5000|g" docker-compose.prod.yml
            sed -i "s|fes_crm_prod|fes_crm_green|g" docker-compose.prod.yml

            # Start green environment
            sudo docker compose -f docker-compose.prod.yml up -d --build

            # Wait for containers to be ready
            log "Waiting for green environment to be ready..."
            sleep 30

            # Health check function
            health_check() {
              local port=$1
              local retries=$2
              local interval=$3

              for i in $(seq 1 $retries); do
                if curl -f -s "http://localhost:${port}/" > /dev/null 2>&1; then
                  log "Health check passed on port ${port} (attempt ${i}/${retries})"
                  return 0
                fi
                warn "Health check failed on port ${port} (attempt ${i}/${retries})"
                sleep $interval
              done

              error "Health check failed after ${retries} attempts on port ${port}"
              return 1
            }

            # Comprehensive health checks for green environment
            log "Running comprehensive health checks on green environment..."

            if ! health_check ${GREEN_PORT} ${{ env.HEALTH_CHECK_RETRIES }} ${{ env.HEALTH_CHECK_INTERVAL }}; then
              error "Green environment health check failed"

              # Cleanup failed green deployment
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans
              sudo docker image rm fes-crm-backend:${NEW_VERSION} || true

              exit 1
            fi

            # Run smoke tests
            log "Running smoke tests on green environment..."

            # Test API endpoints
            if ! curl -f -s "http://localhost:${GREEN_PORT}/" | grep -q "Hello World"; then
              error "Smoke test failed: API not responding correctly"
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans
              exit 1
            fi

            # Test database connectivity
            if ! sudo docker compose -f docker-compose.prod.yml exec -T backend npx prisma db pull --schema=./prisma/schema.prisma > /dev/null 2>&1; then
              error "Smoke test failed: Database connectivity issue"
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans
              exit 1
            fi

            log "All smoke tests passed on green environment"

            # Traffic switch: Stop blue environment and start green on main port
            log "Switching traffic from blue to green environment..."

            # Stop current blue environment
            if [ -d "${APP_DIR}" ]; then
              cd "${APP_DIR}"
              sudo docker compose -f docker-compose.prod.yml down --remove-orphans || true
            fi

            # Move new deployment to main directory
            cd /home/<USER>
            if [ -d "${APP_DIR}" ]; then
              sudo rm -rf "${APP_DIR}"
            fi
            sudo mv "${NEW_DIR}" "${APP_DIR}"

            # Update docker-compose to use main port
            cd "${APP_DIR}"
            sed -i "s|${GREEN_PORT}:5000|5000:5000|g" docker-compose.prod.yml
            sed -i "s|fes_crm_green|fes_crm_prod|g" docker-compose.prod.yml

            # Start on main port
            sudo docker compose -f docker-compose.prod.yml up -d

            # Final health check on main port
            log "Running final health check on main port..."

            if ! health_check 5000 ${{ env.HEALTH_CHECK_RETRIES }} ${{ env.HEALTH_CHECK_INTERVAL }}; then
              error "Final health check failed on main port"

              # Emergency rollback
              if [ -d "${BACKUP_DIR}" ]; then
                warn "Performing emergency rollback..."
                sudo docker compose -f docker-compose.prod.yml down --remove-orphans
                sudo rm -rf "${APP_DIR}"
                sudo mv "${BACKUP_DIR}" "${APP_DIR}"
                cd "${APP_DIR}"
                sudo docker compose -f docker-compose.prod.yml up -d
              fi

              exit 1
            fi

            # Save version info
            echo "${NEW_VERSION}" > "${APP_DIR}/.version"
            echo "$(date)" > "${APP_DIR}/.deployment-time"

            # Cleanup
            log "Cleaning up old images and containers..."
            sudo docker system prune -f
            sudo docker image prune -f

            # Remove backup after successful deployment (keep last 3)
            find /home/<USER>"fes-crm-backend-backup-*" -type d | sort | head -n -3 | xargs sudo rm -rf

            log "Zero-downtime deployment completed successfully!"
            log "New version ${NEW_VERSION} is now live"

            # Display final status
            sudo docker compose -f docker-compose.prod.yml ps

      - name: Post-deployment validation
        id: validation
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            log() { echo -e "\033[0;32m[$(date +'%Y-%m-%d %H:%M:%S')] $1\033[0m"; }

            log "Running post-deployment validation..."

            # Wait for application to fully stabilize
            sleep 15

            # Comprehensive integration tests
            log "Running integration tests..."

            # Test 1: API Health
            if ! curl -f -s "http://localhost:5000/" > /dev/null; then
              echo "VALIDATION_FAILED: API health check failed" >> $GITHUB_OUTPUT
              exit 1
            fi

            # Test 2: Database connectivity
            cd /home/<USER>/fes-crm-backend
            if ! sudo docker compose -f docker-compose.prod.yml exec -T backend npx prisma db pull --schema=./prisma/schema.prisma > /dev/null 2>&1; then
              echo "VALIDATION_FAILED: Database connectivity test failed" >> $GITHUB_OUTPUT
              exit 1
            fi

            # Test 3: Container health
            if ! sudo docker compose -f docker-compose.prod.yml ps | grep -q "healthy\|Up"; then
              echo "VALIDATION_FAILED: Container health check failed" >> $GITHUB_OUTPUT
              exit 1
            fi

            # Test 4: Memory and CPU usage
            MEMORY_USAGE=$(sudo docker stats --no-stream --format "{{.MemPerc}}" fes_crm_prod | sed 's/%//')
            if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
              echo "VALIDATION_WARNING: High memory usage detected: ${MEMORY_USAGE}%" >> $GITHUB_OUTPUT
            fi

            log "All post-deployment validations passed"
            echo "VALIDATION_PASSED=true" >> $GITHUB_OUTPUT

      - name: Set deployment result
        id: deployment-result
        run: |
          if [ "${{ steps.deploy.outcome }}" == "success" ] && [ "${{ steps.validation.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failed" >> $GITHUB_OUTPUT
          fi

  rollback:
    name: Automatic Rollback
    runs-on: ubuntu-latest
    needs: deploy
    if: failure() && needs.deploy.outputs.deployment-status == 'failed'
    steps:
      - name: Rollback to previous version
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: 22
          script: |
            log() { echo -e "\033[0;33m[$(date +'%Y-%m-%d %H:%M:%S')] ROLLBACK: $1\033[0m"; }
            error() { echo -e "\033[0;31m[$(date +'%Y-%m-%d %H:%M:%S')] ROLLBACK ERROR: $1\033[0m"; }

            log "Starting automatic rollback procedure..."

            # Find latest backup
            LATEST_BACKUP=$(find /home/<USER>"fes-crm-backend-backup-*" -type d | sort | tail -1)

            if [ -z "$LATEST_BACKUP" ]; then
              error "No backup found for rollback"
              exit 1
            fi

            log "Rolling back to: $LATEST_BACKUP"

            # Stop current deployment
            cd /home/<USER>/fes-crm-backend
            sudo docker compose -f docker-compose.prod.yml down --remove-orphans

            # Restore from backup
            cd /home/<USER>
            sudo rm -rf fes-crm-backend
            sudo cp -r "$LATEST_BACKUP" fes-crm-backend

            # Start restored version
            cd fes-crm-backend
            sudo docker compose -f docker-compose.prod.yml up -d

            # Health check after rollback
            sleep 30
            for i in {1..10}; do
              if curl -f -s "http://localhost:5000/" > /dev/null; then
                log "Rollback successful - application is healthy"
                exit 0
              fi
              sleep 10
            done

            error "Rollback failed - application not responding"
            exit 1

  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [deploy, rollback]
    if: always()
    steps:
      - name: Notify deployment result
        if: env.SLACK_WEBHOOK != ''
        run: |
          if [ "${{ needs.deploy.outputs.deployment-status }}" == "success" ]; then
            STATUS_EMOJI="✅"
            STATUS_TEXT="SUCCESS"
            COLOR="good"
          else
            STATUS_EMOJI="❌"
            STATUS_TEXT="FAILED"
            COLOR="danger"
          fi

          if [ "${{ needs.rollback.result }}" == "success" ]; then
            ROLLBACK_TEXT="\n🔄 Automatic rollback completed successfully"
          elif [ "${{ needs.rollback.result }}" == "failure" ]; then
            ROLLBACK_TEXT="\n💥 Automatic rollback failed - manual intervention required"
          else
            ROLLBACK_TEXT=""
          fi

          curl -X POST -H 'Content-type: application/json' \
            --data "{
              \"attachments\": [{
                \"color\": \"$COLOR\",
                \"title\": \"$STATUS_EMOJI FES CRM Backend Deployment $STATUS_TEXT\",
                \"fields\": [
                  {\"title\": \"Commit\", \"value\": \"${{ github.sha }}\", \"short\": true},
                  {\"title\": \"Branch\", \"value\": \"${{ github.ref_name }}\", \"short\": true},
                  {\"title\": \"Actor\", \"value\": \"${{ github.actor }}\", \"short\": true},
                  {\"title\": \"Environment\", \"value\": \"Production\", \"short\": true}
                ],
                \"text\": \"Deployment to production server completed.$ROLLBACK_TEXT\",
                \"footer\": \"GitHub Actions\",
                \"ts\": $(date +%s)
              }]
            }" \
            ${{ env.SLACK_WEBHOOK }}