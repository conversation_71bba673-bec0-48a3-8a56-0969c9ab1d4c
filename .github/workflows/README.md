# GitHub Actions Workflows

This directory contains the CI/CD workflows for the FES CRM Backend project.

## Workflows

### 1. CI Pipeline (`ci.yml`)
**Triggers:** Push to `main`/`develop` branches, Pull Requests

**Features:**
- ✅ Smart npm package caching based on `package-lock.json` hash
- ✅ Conditional npm install (only when dependencies change)
- ✅ External PostgreSQL database for testing (no Docker required)
- ✅ Prisma client generation and database migrations
- ✅ Code linting and formatting checks
- ✅ Unit and E2E test execution
- ✅ Build verification
- ✅ Test coverage reporting

**Jobs:**
1. **Test and Build**: Runs all tests with external PostgreSQL service
2. **Build Check**: Verifies production build artifacts

### 2. Deployment (`deploy.yml`)
**Triggers:** Push to `develop` branch

**Features:**
- ✅ Deploys to AWS EC2 without Docker database dependency
- ✅ Uses external database configuration
- ✅ PM2 process management for application restart
- ✅ Automated database migrations and seeding

### 3. Security and Dependency Check (`security.yml`)
**Triggers:** Weekly schedule, Push to `main`, Pull Requests

**Features:**
- ✅ npm security audit
- ✅ Dependency review for pull requests
- ✅ Outdated package detection

## Environment Variables

The CI pipeline uses the following environment variables for testing:

```env
DATABASE_URL=postgres://fes_crm_admin:super-secret-123@localhost:5432/fes_crm_test
NODE_ENV=test
BACKEND_PORT=5001
ENABLE_AUTH=FALSE
JWT_SECRET=asdvgfcvblcvbrlqwedkamklmbrwewoved
JWT_EXPIRES=1d
```

## Caching Strategy

### npm Dependencies
- **Cache Key**: `${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}`
- **Path**: `~/.npm` and `node_modules`
- **Behavior**: Only runs `npm ci` when `package-lock.json` changes

### Benefits
- ⚡ Faster build times (2-3x speedup when cache hits)
- 💰 Reduced GitHub Actions minutes usage
- 🔄 Automatic cache invalidation when dependencies change

## Database Strategy

### Testing
- Uses GitHub Actions PostgreSQL service
- No Docker containers required
- Isolated test database per workflow run
- Automatic cleanup after tests complete

### Deployment
- Connects to external PostgreSQL database
- No Docker database service deployed
- Uses environment variables for database configuration

## Monitoring and Reporting

- **Test Coverage**: Uploaded to Codecov (optional)
- **Build Artifacts**: Verified in separate job
- **Security**: Weekly dependency audits
- **Performance**: Cached builds for faster execution

## Usage

### Running Tests Locally
```bash
# Install dependencies
npm ci

# Generate Prisma client
npx prisma generate

# Run tests
npm run test
npm run test:e2e

# Run with coverage
npm run test:cov
```

### Manual Deployment
The deployment workflow can be triggered manually or automatically on push to `develop` branch.

## Troubleshooting

### Cache Issues
If you encounter caching issues, you can clear the cache by:
1. Updating `package-lock.json`
2. Or manually clearing cache in GitHub repository settings

### Database Connection Issues
Ensure your external database:
- Is accessible from the deployment environment
- Has correct credentials in environment variables
- Allows connections from the application server

### Build Failures
Check the following:
- All environment variables are properly set
- Database migrations are up to date
- No TypeScript compilation errors
- All tests are passing locally
