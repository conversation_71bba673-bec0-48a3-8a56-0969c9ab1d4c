name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '20'

jobs:
  test:
    name: Test and Build
    runs-on: ubuntu-latest

    # External PostgreSQL service for testing
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: fes_crm_admin
          POSTGRES_PASSWORD: super-secret-123
          POSTGRES_DB: fes_crm_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Get npm cache directory
        id: npm-cache-dir
        shell: bash
        run: echo "dir=$(npm config get cache)" >> ${GITHUB_OUTPUT}

      - name: Cache npm dependencies
        uses: actions/cache@v4
        id: npm-cache
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Cache node_modules
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Install dependencies
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Run database migrations
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: postgres://fes_crm_admin:super-secret-123@localhost:5432/fes_crm_test

      - name: Seed database
        run: npm run migrate
        env:
          DATABASE_URL: postgres://fes_crm_admin:super-secret-123@localhost:5432/fes_crm_test
          NODE_ENV: test
          BACKEND_PORT: 5001
          ENABLE_AUTH: FALSE
          JWT_SECRET: asdvgfcvblcvbrlqwedkamklmbrwewoved
          JWT_EXPIRES: 1d

      - name: Run linting
        run: npm run lint

      - name: Check code formatting
        run: npm run format -- --check

      - name: Build application
        run: npm run build

      - name: Run unit tests
        run: npm run test -- --coverage
        env:
          DATABASE_URL: postgres://fes_crm_admin:super-secret-123@localhost:5432/fes_crm_test
          NODE_ENV: test
          BACKEND_PORT: 5001
          ENABLE_AUTH: FALSE
          JWT_SECRET: asdvgfcvblcvbrlqwedkamklmbrwewoved
          JWT_EXPIRES: 1d

      - name: Run e2e tests
        run: npm run test:e2e
        env:
          DATABASE_URL: postgres://fes_crm_admin:super-secret-123@localhost:5432/fes_crm_test
          NODE_ENV: test
          BACKEND_PORT: 5001
          ENABLE_AUTH: FALSE
          JWT_SECRET: asdvgfcvblcvbrlqwedkamklmbrwewoved
          JWT_EXPIRES: 1d

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        if: success()
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  build-check:
    name: Build Check
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Cache node_modules
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}

      - name: Install dependencies
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Build for production
        run: npm run build

      - name: Check build artifacts
        run: |
          if [ ! -d "dist" ]; then
            echo "Build failed: dist directory not found"
            exit 1
          fi
          echo "Build successful: dist directory created"
          ls -la dist/
