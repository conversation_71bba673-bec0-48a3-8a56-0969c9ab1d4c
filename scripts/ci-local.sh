#!/bin/bash

# Local CI simulation script
# This script mimics the CI pipeline for local development

set -e

echo "🚀 Starting local CI simulation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${YELLOW}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if .env.test exists
if [ ! -f ".env.test" ]; then
    print_error ".env.test file not found. Creating from template..."
    cp config.env .env.test
    print_success "Created .env.test from config.env"
fi

# Load environment variables
export $(cat .env.test | grep -v '^#' | xargs)

print_step "Installing dependencies..."
npm ci
print_success "Dependencies installed"

print_step "Generating Prisma client..."
npx prisma generate
print_success "Prisma client generated"

print_step "Running linting..."
if npm run lint; then
    print_success "Linting passed"
else
    print_error "Linting failed"
    exit 1
fi

print_step "Checking code formatting..."
if npm run format -- --check; then
    print_success "Code formatting is correct"
else
    print_error "Code formatting issues found. Run 'npm run format' to fix."
    exit 1
fi

print_step "Building application..."
if npm run build; then
    print_success "Build successful"
else
    print_error "Build failed"
    exit 1
fi

print_step "Running unit tests..."
if npm run test -- --coverage; then
    print_success "Unit tests passed"
else
    print_error "Unit tests failed"
    exit 1
fi

print_step "Running e2e tests..."
if npm run test:e2e; then
    print_success "E2E tests passed"
else
    print_error "E2E tests failed"
    exit 1
fi

print_success "🎉 All checks passed! Your code is ready for CI."

echo ""
echo "📊 Coverage report available at: coverage/lcov-report/index.html"
echo "🏗️  Build artifacts available at: dist/"
