@echo off
setlocal enabledelayedexpansion

echo 🚀 Starting local CI simulation...

REM Check if .env.test exists
if not exist ".env.test" (
    echo ❌ .env.test file not found. Creating from template...
    copy config.env .env.test
    echo ✅ Created .env.test from config.env
)

echo 📋 Installing dependencies...
call npm ci
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    exit /b 1
)
echo ✅ Dependencies installed

echo 📋 Generating Prisma client...
call npx prisma generate
if errorlevel 1 (
    echo ❌ Failed to generate Prisma client
    exit /b 1
)
echo ✅ Prisma client generated

echo 📋 Running linting...
call npm run lint
if errorlevel 1 (
    echo ❌ Linting failed
    exit /b 1
)
echo ✅ Linting passed

echo 📋 Checking code formatting...
call npm run format -- --check
if errorlevel 1 (
    echo ❌ Code formatting issues found. Run 'npm run format' to fix.
    exit /b 1
)
echo ✅ Code formatting is correct

echo 📋 Building application...
call npm run build
if errorlevel 1 (
    echo ❌ Build failed
    exit /b 1
)
echo ✅ Build successful

echo 📋 Running unit tests...
call npm run test -- --coverage
if errorlevel 1 (
    echo ❌ Unit tests failed
    exit /b 1
)
echo ✅ Unit tests passed

echo 📋 Running e2e tests...
call npm run test:e2e
if errorlevel 1 (
    echo ❌ E2E tests failed
    exit /b 1
)
echo ✅ E2E tests passed

echo.
echo 🎉 All checks passed! Your code is ready for CI.
echo.
echo 📊 Coverage report available at: coverage/lcov-report/index.html
echo 🏗️  Build artifacts available at: dist/

pause
