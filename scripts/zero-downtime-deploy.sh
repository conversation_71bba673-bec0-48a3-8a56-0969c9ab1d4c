#!/bin/bash

# Zero-Downtime Deployment Script
# This script implements blue-green deployment strategy

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="fes-crm-backend"
DEPLOYMENT_TIMEOUT=600
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_INTERVAL=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Help function
show_help() {
    cat << EOF
Zero-Downtime Deployment Script for FES CRM Backend

Usage: $0 [OPTIONS]

OPTIONS:
    -v, --version VERSION    Docker image version/tag to deploy
    -e, --env-file FILE      Environment file path (default: .env)
    -t, --timeout SECONDS   Deployment timeout (default: 600)
    -r, --retries COUNT     Health check retries (default: 30)
    -i, --interval SECONDS  Health check interval (default: 10)
    -f, --force             Force deployment without confirmation
    -d, --dry-run           Show what would be done without executing
    -h, --help              Show this help message

EXAMPLES:
    $0 -v latest                    # Deploy latest version
    $0 -v abc123 -f                 # Force deploy specific commit
    $0 -v latest -d                 # Dry run deployment
    $0 -v latest -t 900 -r 50       # Custom timeout and retries

EOF
}

# Parse command line arguments
VERSION=""
ENV_FILE=".env"
FORCE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -e|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        -t|--timeout)
            DEPLOYMENT_TIMEOUT="$2"
            shift 2
            ;;
        -r|--retries)
            HEALTH_CHECK_RETRIES="$2"
            shift 2
            ;;
        -i|--interval)
            HEALTH_CHECK_INTERVAL="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$VERSION" ]]; then
    error "Version is required. Use -v or --version to specify."
    show_help
    exit 1
fi

if [[ ! -f "$ENV_FILE" ]]; then
    error "Environment file not found: $ENV_FILE"
    exit 1
fi

# Load environment variables
set -a
source "$ENV_FILE"
set +a

# Dry run mode
if [[ "$DRY_RUN" == "true" ]]; then
    info "DRY RUN MODE - No actual changes will be made"
    info "Would deploy version: $VERSION"
    info "Using environment file: $ENV_FILE"
    info "Deployment timeout: ${DEPLOYMENT_TIMEOUT}s"
    info "Health check retries: $HEALTH_CHECK_RETRIES"
    info "Health check interval: ${HEALTH_CHECK_INTERVAL}s"
    exit 0
fi

# Confirmation prompt
if [[ "$FORCE" != "true" ]]; then
    echo
    info "Deployment Configuration:"
    echo "  Version: $VERSION"
    echo "  Environment: $ENV_FILE"
    echo "  Timeout: ${DEPLOYMENT_TIMEOUT}s"
    echo "  Health checks: $HEALTH_CHECK_RETRIES retries every ${HEALTH_CHECK_INTERVAL}s"
    echo
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        warn "Deployment cancelled by user"
        exit 0
    fi
fi

# Health check function
health_check() {
    local port=$1
    local retries=$2
    local interval=$3
    local service_name=${4:-"service"}
    
    info "Running health checks for $service_name on port $port..."
    
    for i in $(seq 1 $retries); do
        if curl -f -s "http://localhost:${port}/" > /dev/null 2>&1; then
            log "Health check passed for $service_name on port $port (attempt $i/$retries)"
            return 0
        fi
        warn "Health check failed for $service_name on port $port (attempt $i/$retries)"
        sleep $interval
    done
    
    error "Health check failed after $retries attempts for $service_name on port $port"
    return 1
}

# Smoke tests function
run_smoke_tests() {
    local port=$1
    local service_name=${2:-"service"}
    
    info "Running smoke tests for $service_name on port $port..."
    
    # Test 1: API Health
    if ! curl -f -s "http://localhost:${port}/" | grep -q "Hello World"; then
        error "Smoke test failed: API not responding correctly for $service_name"
        return 1
    fi
    
    # Test 2: API Response Time
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:${port}/")
    if (( $(echo "$response_time > 5.0" | bc -l) )); then
        warn "Slow response time detected: ${response_time}s for $service_name"
    fi
    
    log "All smoke tests passed for $service_name"
    return 0
}

# Cleanup function
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        error "Deployment failed with exit code $exit_code"
        warn "Check logs and consider manual rollback if necessary"
    fi
    exit $exit_code
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment function
main() {
    log "Starting zero-downtime deployment for $APP_NAME version $VERSION"
    
    # Pre-deployment checks
    info "Running pre-deployment checks..."
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        error "Docker is not running or not accessible"
        exit 1
    fi
    
    # Check if required image exists
    if ! docker image inspect "${APP_NAME}:${VERSION}" > /dev/null 2>&1; then
        error "Docker image ${APP_NAME}:${VERSION} not found"
        exit 1
    fi
    
    # Get current deployment info
    CURRENT_VERSION=""
    if [[ -f ".version" ]]; then
        CURRENT_VERSION=$(cat .version)
        info "Current version: $CURRENT_VERSION"
    else
        info "No previous deployment found"
    fi
    
    # Create backup timestamp
    BACKUP_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    BACKUP_DIR="../${APP_NAME}-backup-${BACKUP_TIMESTAMP}"
    
    # Backup current deployment
    if [[ -n "$CURRENT_VERSION" ]]; then
        info "Creating backup of current deployment..."
        cp -r . "$BACKUP_DIR"
        log "Backup created: $BACKUP_DIR"
    fi
    
    # Database migration safety check
    info "Checking database migration safety..."
    if ! docker run --rm --env-file "$ENV_FILE" --network host \\\n        \"${APP_NAME}:${VERSION}\" \\\n        sh -c \"npx prisma migrate deploy --schema=./prisma/schema.prisma\" > /dev/null 2>&1; then\n        error \"Database migration failed or unsafe\"\n        exit 1\n    fi\n    log \"Database migrations completed successfully\"\n    \n    # Blue-Green Deployment Strategy\n    info \"Starting blue-green deployment...\"\n    \n    # Step 1: Start green environment on different port\n    GREEN_PORT=5001\n    GREEN_COMPOSE_FILE=\"docker-compose.green.yml\"\n    \n    # Create green environment compose file\n    sed \"s|5000:5000|${GREEN_PORT}:5000|g; s|fes_crm_prod|fes_crm_green|g; s|${APP_NAME}:.*|${APP_NAME}:${VERSION}|g\" \\\n        docker-compose.prod.yml > \"$GREEN_COMPOSE_FILE\"\n    \n    info \"Starting green environment on port $GREEN_PORT...\"\n    docker compose -f \"$GREEN_COMPOSE_FILE\" up -d\n    \n    # Wait for green environment to be ready\n    info \"Waiting for green environment to initialize...\"\n    sleep 30\n    \n    # Health check green environment\n    if ! health_check $GREEN_PORT $HEALTH_CHECK_RETRIES $HEALTH_CHECK_INTERVAL \"green environment\"; then\n        error \"Green environment health check failed\"\n        docker compose -f \"$GREEN_COMPOSE_FILE\" down --remove-orphans\n        rm -f \"$GREEN_COMPOSE_FILE\"\n        exit 1\n    fi\n    \n    # Run smoke tests on green environment\n    if ! run_smoke_tests $GREEN_PORT \"green environment\"; then\n        error \"Green environment smoke tests failed\"\n        docker compose -f \"$GREEN_COMPOSE_FILE\" down --remove-orphans\n        rm -f \"$GREEN_COMPOSE_FILE\"\n        exit 1\n    fi\n    \n    # Step 2: Traffic switch - Stop blue, start green on main port\n    info \"Switching traffic from blue to green environment...\"\n    \n    # Stop blue environment\n    if docker compose -f docker-compose.prod.yml ps | grep -q \"Up\"; then\n        info \"Stopping blue environment...\"\n        docker compose -f docker-compose.prod.yml down --remove-orphans\n    fi\n    \n    # Update main compose file with new version\n    sed -i \"s|${APP_NAME}:.*|${APP_NAME}:${VERSION}|g\" docker-compose.prod.yml\n    \n    # Start new version on main port\n    info \"Starting new version on main port...\"\n    docker compose -f docker-compose.prod.yml up -d\n    \n    # Cleanup green environment\n    docker compose -f \"$GREEN_COMPOSE_FILE\" down --remove-orphans\n    rm -f \"$GREEN_COMPOSE_FILE\"\n    \n    # Final health check on main port\n    info \"Running final health checks on main port...\"\n    if ! health_check 5000 $HEALTH_CHECK_RETRIES $HEALTH_CHECK_INTERVAL \"production environment\"; then\n        error \"Final health check failed on main port\"\n        \n        # Emergency rollback\n        if [[ -d \"$BACKUP_DIR\" ]]; then\n            warn \"Performing emergency rollback...\"\n            docker compose -f docker-compose.prod.yml down --remove-orphans\n            cp -r \"$BACKUP_DIR\"/* .\n            docker compose -f docker-compose.prod.yml up -d\n            \n            if health_check 5000 10 5 \"rollback environment\"; then\n                log \"Emergency rollback successful\"\n            else\n                error \"Emergency rollback failed - manual intervention required\"\n            fi\n        fi\n        exit 1\n    fi\n    \n    # Final smoke tests\n    if ! run_smoke_tests 5000 \"production environment\"; then\n        warn \"Production smoke tests failed, but deployment will continue\"\n    fi\n    \n    # Save deployment info\n    echo \"$VERSION\" > .version\n    echo \"$(date)\" > .deployment-time\n    echo \"$BACKUP_TIMESTAMP\" > .last-backup\n    \n    # Cleanup old backups (keep last 5)\n    find .. -name \"${APP_NAME}-backup-*\" -type d | sort | head -n -5 | xargs rm -rf 2>/dev/null || true\n    \n    # Cleanup Docker resources\n    info \"Cleaning up Docker resources...\"\n    docker system prune -f > /dev/null 2>&1 || true\n    docker image prune -f > /dev/null 2>&1 || true\n    \n    # Display final status\n    log \"Zero-downtime deployment completed successfully!\"\n    log \"New version $VERSION is now live\"\n    \n    info \"Deployment Summary:\"\n    echo \"  Previous version: ${CURRENT_VERSION:-none}\"\n    echo \"  New version: $VERSION\"\n    echo \"  Backup location: $BACKUP_DIR\"\n    echo \"  Deployment time: $(date)\"\n    \n    # Show running containers\n    info \"Running containers:\"\n    docker compose -f docker-compose.prod.yml ps\n}\n\n# Run main function\nmain \"$@\"
