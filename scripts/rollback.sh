#!/bin/bash

# Rollback Script for FES CRM Backend
# Provides safe rollback to previous versions

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="fes-crm-backend"
ROLLBACK_TIMEOUT=300

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Help function
show_help() {
    cat << EOF
Rollback Script for FES CRM Backend

Usage: $0 [OPTIONS]

OPTIONS:
    -v, --version VERSION    Specific version to rollback to
    -b, --backup BACKUP      Backup directory to restore from
    -l, --list               List available backups and versions
    -f, --force              Force rollback without confirmation
    -d, --dry-run            Show what would be done without executing
    -t, --timeout SECONDS    Rollback timeout (default: 300)
    -h, --help               Show this help message

EXAMPLES:
    $0 -l                    # List available backups
    $0 -v abc123             # Rollback to specific version
    $0 -b backup-20240101    # Rollback to specific backup
    $0 -f                    # Force rollback to latest backup
    $0 -d -v abc123          # Dry run rollback

EOF
}

# List available backups and versions
list_backups() {
    info "Available backups:"
    find .. -maxdepth 1 -name "${APP_NAME}-backup-*" -type d | sort -r | while read -r backup; do
        local backup_name=$(basename "$backup")
        local backup_date=$(echo "$backup_name" | sed "s/${APP_NAME}-backup-//")
        
        # Try to get version from backup
        local version="unknown"
        if [[ -f "$backup/.version" ]]; then
            version=$(cat "$backup/.version")
        fi
        
        echo "  $backup_name (version: $version, date: $backup_date)"
    done
    
    echo
    info "Current deployment:"
    if [[ -f ".version" ]]; then
        local current_version=$(cat .version)
        local deployment_time="unknown"
        if [[ -f ".deployment-time" ]]; then
            deployment_time=$(cat .deployment-time)
        fi
        echo "  Version: $current_version"
        echo "  Deployed: $deployment_time"
    else
        echo "  No version information available"
    fi
}

# Health check function
health_check() {
    local retries=${1:-10}
    local interval=${2:-5}
    
    for i in $(seq 1 $retries); do
        if curl -f -s "http://localhost:5000/" > /dev/null 2>&1; then
            log "Health check passed (attempt $i/$retries)"
            return 0
        fi
        warn "Health check failed (attempt $i/$retries)"
        sleep $interval
    done
    
    error "Health check failed after $retries attempts"
    return 1
}

# Database rollback safety check
check_database_rollback_safety() {
    local target_version="$1"
    
    info "Checking database rollback safety for version $target_version..."
    
    # This is a placeholder for database rollback safety checks
    # In a real scenario, you would:
    # 1. Check if the target version's schema is compatible
    # 2. Verify no destructive migrations have been run since
    # 3. Ensure data integrity won't be compromised
    
    warn "Database rollback safety check is not implemented"
    warn "Manual verification of database compatibility is recommended"
    
    return 0
}

# Rollback to specific version
rollback_to_version() {
    local target_version="$1"
    local force=${2:-false}
    
    info "Rolling back to version: $target_version"
    
    # Check if the version exists as a Docker image
    if ! docker image inspect "${APP_NAME}:${target_version}" > /dev/null 2>&1; then
        error "Docker image ${APP_NAME}:${target_version} not found"
        return 1
    fi
    
    # Database rollback safety check
    if ! check_database_rollback_safety "$target_version"; then
        if [[ "$force" != "true" ]]; then
            error "Database rollback safety check failed. Use --force to override."
            return 1
        fi
        warn "Proceeding with rollback despite safety check failure"
    fi
    
    # Get current version for backup
    local current_version="unknown"
    if [[ -f ".version" ]]; then
        current_version=$(cat .version)
    fi
    
    # Create emergency backup before rollback
    local emergency_backup="../${APP_NAME}-emergency-backup-$(date +%Y%m%d-%H%M%S)"
    info "Creating emergency backup before rollback..."
    cp -r . "$emergency_backup"
    log "Emergency backup created: $emergency_backup"
    
    # Update docker-compose with target version
    info "Updating Docker Compose configuration..."
    sed -i "s|${APP_NAME}:.*|${APP_NAME}:${target_version}|g" docker-compose.prod.yml
    
    # Stop current deployment\n    info \"Stopping current deployment...\"\n    docker compose -f docker-compose.prod.yml down --remove-orphans\n    \n    # Start rollback version\n    info \"Starting rollback version...\"\n    docker compose -f docker-compose.prod.yml up -d\n    \n    # Wait for application to start\n    info \"Waiting for application to start...\"\n    sleep 30\n    \n    # Health check\n    if ! health_check 15 10; then\n        error \"Rollback health check failed\"\n        \n        # Attempt to restore from emergency backup\n        warn \"Attempting to restore from emergency backup...\"\n        docker compose -f docker-compose.prod.yml down --remove-orphans\n        \n        # Restore emergency backup\n        cp -r \"$emergency_backup\"/* .\n        docker compose -f docker-compose.prod.yml up -d\n        \n        if health_check 10 5; then\n            log \"Successfully restored from emergency backup\"\n        else\n            error \"Failed to restore from emergency backup - manual intervention required\"\n        fi\n        \n        return 1\n    fi\n    \n    # Update version info\n    echo \"$target_version\" > .version\n    echo \"$(date)\" > .rollback-time\n    echo \"$current_version\" > .previous-version\n    \n    log \"Rollback to version $target_version completed successfully\"\n    \n    # Cleanup emergency backup after successful rollback\n    rm -rf \"$emergency_backup\"\n    \n    return 0\n}\n\n# Rollback to specific backup\nrollback_to_backup() {\n    local backup_path=\"$1\"\n    local force=${2:-false}\n    \n    if [[ ! -d \"$backup_path\" ]]; then\n        error \"Backup directory not found: $backup_path\"\n        return 1\n    fi\n    \n    info \"Rolling back to backup: $backup_path\"\n    \n    # Get version from backup if available\n    local target_version=\"unknown\"\n    if [[ -f \"$backup_path/.version\" ]]; then\n        target_version=$(cat \"$backup_path/.version\")\n    fi\n    \n    # Database rollback safety check\n    if [[ \"$target_version\" != \"unknown\" ]]; then\n        if ! check_database_rollback_safety \"$target_version\"; then\n            if [[ \"$force\" != \"true\" ]]; then\n                error \"Database rollback safety check failed. Use --force to override.\"\n                return 1\n            fi\n            warn \"Proceeding with rollback despite safety check failure\"\n        fi\n    fi\n    \n    # Create emergency backup before rollback\n    local emergency_backup=\"../${APP_NAME}-emergency-backup-$(date +%Y%m%d-%H%M%S)\"\n    info \"Creating emergency backup before rollback...\"\n    cp -r . \"$emergency_backup\"\n    log \"Emergency backup created: $emergency_backup\"\n    \n    # Stop current deployment\n    info \"Stopping current deployment...\"\n    docker compose -f docker-compose.prod.yml down --remove-orphans\n    \n    # Restore from backup\n    info \"Restoring from backup...\"\n    \n    # Remove current files (except .env)\n    find . -mindepth 1 -maxdepth 1 ! -name '.env' -exec rm -rf {} +\n    \n    # Copy backup files\n    cp -r \"$backup_path\"/* .\n    \n    # Restore .env if it doesn't exist in backup\n    if [[ ! -f \".env\" ]] && [[ -f \"$emergency_backup/.env\" ]]; then\n        cp \"$emergency_backup/.env\" .\n        log \"Restored .env from emergency backup\"\n    fi\n    \n    # Start restored version\n    info \"Starting restored version...\"\n    docker compose -f docker-compose.prod.yml up -d\n    \n    # Wait for application to start\n    info \"Waiting for application to start...\"\n    sleep 30\n    \n    # Health check\n    if ! health_check 15 10; then\n        error \"Rollback health check failed\"\n        \n        # Attempt to restore from emergency backup\n        warn \"Attempting to restore from emergency backup...\"\n        docker compose -f docker-compose.prod.yml down --remove-orphans\n        \n        # Remove failed restore\n        find . -mindepth 1 -maxdepth 1 ! -name '.env' -exec rm -rf {} +\n        \n        # Restore emergency backup\n        cp -r \"$emergency_backup\"/* .\n        docker compose -f docker-compose.prod.yml up -d\n        \n        if health_check 10 5; then\n            log \"Successfully restored from emergency backup\"\n        else\n            error \"Failed to restore from emergency backup - manual intervention required\"\n        fi\n        \n        return 1\n    fi\n    \n    # Update rollback info\n    echo \"$(date)\" > .rollback-time\n    echo \"backup:$(basename \"$backup_path\")\" > .rollback-source\n    \n    log \"Rollback to backup $(basename \"$backup_path\") completed successfully\"\n    \n    # Cleanup emergency backup after successful rollback\n    rm -rf \"$emergency_backup\"\n    \n    return 0\n}\n\n# Automatic rollback to latest backup\nauto_rollback() {\n    local force=${1:-false}\n    \n    info \"Performing automatic rollback to latest backup...\"\n    \n    # Find latest backup\n    local latest_backup=$(find .. -maxdepth 1 -name \"${APP_NAME}-backup-*\" -type d | sort -r | head -1)\n    \n    if [[ -z \"$latest_backup\" ]]; then\n        error \"No backups found for automatic rollback\"\n        return 1\n    fi\n    \n    info \"Latest backup found: $(basename \"$latest_backup\")\"\n    \n    rollback_to_backup \"$latest_backup\" \"$force\"\n}\n\n# Send notification\nsend_notification() {\n    local message=\"$1\"\n    local severity=\"${2:-info}\"\n    \n    # Slack notification\n    if [[ -n \"${SLACK_WEBHOOK:-}\" ]]; then\n        local emoji=\"ℹ️\"\n        local color=\"good\"\n        \n        case \"$severity\" in\n            \"success\") emoji=\"✅\"; color=\"good\" ;;\n            \"warning\") emoji=\"⚠️\"; color=\"warning\" ;;\n            \"error\") emoji=\"❌\"; color=\"danger\" ;;\n        esac\n        \n        curl -X POST -H 'Content-type: application/json' \\\n            --data \"{\n              \\\"attachments\\\": [{\n                \\\"color\\\": \\\"$color\\\",\n                \\\"title\\\": \\\"$emoji FES CRM Backend Rollback\\\",\n                \\\"text\\\": \\\"$message\\\",\n                \\\"footer\\\": \\\"Rollback Script\\\",\n                \\\"ts\\\": $(date +%s)\n              }]\n            }\" \\\n            \"$SLACK_WEBHOOK\" > /dev/null 2>&1 || warn \"Failed to send Slack notification\"\n    fi\n}\n\n# Main function\nmain() {\n    local target_version=\"\"\n    local backup_path=\"\"\n    local list_only=false\n    local force=false\n    local dry_run=false\n    \n    # Parse command line arguments\n    while [[ $# -gt 0 ]]; do\n        case $1 in\n            -v|--version)\n                target_version=\"$2\"\n                shift 2\n                ;;\n            -b|--backup)\n                backup_path=\"$2\"\n                shift 2\n                ;;\n            -l|--list)\n                list_only=true\n                shift\n                ;;\n            -f|--force)\n                force=true\n                shift\n                ;;\n            -d|--dry-run)\n                dry_run=true\n                shift\n                ;;\n            -t|--timeout)\n                ROLLBACK_TIMEOUT=\"$2\"\n                shift 2\n                ;;\n            -h|--help)\n                show_help\n                exit 0\n                ;;\n            *)\n                error \"Unknown option: $1\"\n                show_help\n                exit 1\n                ;;\n        esac\n    done\n    \n    # Change to project root\n    cd \"$PROJECT_ROOT\"\n    \n    # Load environment variables\n    if [[ -f \".env\" ]]; then\n        set -a\n        source \".env\"\n        set +a\n    fi\n    \n    # List backups and exit\n    if [[ \"$list_only\" == \"true\" ]]; then\n        list_backups\n        exit 0\n    fi\n    \n    # Dry run mode\n    if [[ \"$dry_run\" == \"true\" ]]; then\n        info \"DRY RUN MODE - No actual changes will be made\"\n        \n        if [[ -n \"$target_version\" ]]; then\n            info \"Would rollback to version: $target_version\"\n        elif [[ -n \"$backup_path\" ]]; then\n            info \"Would rollback to backup: $backup_path\"\n        else\n            info \"Would perform automatic rollback to latest backup\"\n        fi\n        \n        exit 0\n    fi\n    \n    # Confirmation prompt\n    if [[ \"$force\" != \"true\" ]]; then\n        echo\n        warn \"This will rollback the current deployment!\"\n        \n        if [[ -n \"$target_version\" ]]; then\n            info \"Target version: $target_version\"\n        elif [[ -n \"$backup_path\" ]]; then\n            info \"Target backup: $backup_path\"\n        else\n            info \"Will rollback to latest available backup\"\n        fi\n        \n        echo\n        read -p \"Continue with rollback? (y/N): \" -n 1 -r\n        echo\n        if [[ ! $REPLY =~ ^[Yy]$ ]]; then\n            warn \"Rollback cancelled by user\"\n            exit 0\n        fi\n    fi\n    \n    # Perform rollback\n    local rollback_success=false\n    \n    if [[ -n \"$target_version\" ]]; then\n        if rollback_to_version \"$target_version\" \"$force\"; then\n            rollback_success=true\n            send_notification \"Successfully rolled back to version $target_version\" \"success\"\n        else\n            send_notification \"Failed to rollback to version $target_version\" \"error\"\n        fi\n    elif [[ -n \"$backup_path\" ]]; then\n        # Convert relative path to absolute if needed\n        if [[ \"$backup_path\" != /* ]]; then\n            backup_path=\"../$backup_path\"\n        fi\n        \n        if rollback_to_backup \"$backup_path\" \"$force\"; then\n            rollback_success=true\n            send_notification \"Successfully rolled back to backup $(basename \"$backup_path\")\" \"success\"\n        else\n            send_notification \"Failed to rollback to backup $(basename \"$backup_path\")\" \"error\"\n        fi\n    else\n        if auto_rollback \"$force\"; then\n            rollback_success=true\n            send_notification \"Successfully performed automatic rollback\" \"success\"\n        else\n            send_notification \"Failed to perform automatic rollback\" \"error\"\n        fi\n    fi\n    \n    if [[ \"$rollback_success\" == \"true\" ]]; then\n        log \"Rollback completed successfully\"\n        \n        # Display final status\n        info \"Final deployment status:\"\n        docker compose -f docker-compose.prod.yml ps\n        \n        exit 0\n    else\n        error \"Rollback failed\"\n        exit 1\n    fi\n}\n\n# Run main function\nmain \"$@\"
