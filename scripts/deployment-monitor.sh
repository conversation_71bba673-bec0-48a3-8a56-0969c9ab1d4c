#!/bin/bash

# Deployment Monitoring Script
# Monitors application health and sends alerts

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="fes-crm-backend"
HEALTH_CHECK_INTERVAL=30
ALERT_THRESHOLD=3
LOG_FILE="/var/log/fes-crm-monitor.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { 
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${GREEN}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

warn() { 
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1"
    echo -e "${YELLOW}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

error() { 
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo -e "${RED}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

info() { 
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1"
    echo -e "${BLUE}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# Load environment variables
if [[ -f "$PROJECT_ROOT/.env" ]]; then
    set -a
    source "$PROJECT_ROOT/.env"
    set +a
fi

# Slack notification function
send_slack_alert() {
    local message="$1"
    local severity="${2:-warning}"
    local webhook_url="${SLACK_WEBHOOK:-}"
    
    if [[ -z "$webhook_url" ]]; then
        warn "Slack webhook not configured, skipping notification"
        return 0
    fi
    
    local color="warning"
    local emoji="⚠️"
    
    case "$severity" in
        "critical")
            color="danger"
            emoji="🚨"
            ;;
        "warning")
            color="warning"
            emoji="⚠️"
            ;;
        "info")
            color="good"
            emoji="ℹ️"
            ;;
        "success")
            color="good"
            emoji="✅"
            ;;
    esac
    
    local payload=$(cat << EOF\n{\n  \"attachments\": [{\n    \"color\": \"$color\",\n    \"title\": \"$emoji FES CRM Backend Alert\",\n    \"text\": \"$message\",\n    \"fields\": [\n      {\"title\": \"Server\", \"value\": \"$(hostname)\", \"short\": true},\n      {\"title\": \"Time\", \"value\": \"$(date)\", \"short\": true}\n    ],\n    \"footer\": \"FES CRM Monitor\",\n    \"ts\": $(date +%s)\n  }]\n}\nEOF\n    )\n    \n    curl -X POST -H 'Content-type: application/json' \\\n        --data \"$payload\" \\\n        \"$webhook_url\" > /dev/null 2>&1 || warn \"Failed to send Slack notification\"\n}\n\n# Email notification function\nsend_email_alert() {\n    local subject=\"$1\"\n    local message=\"$2\"\n    local email=\"${ALERT_EMAIL:-}\"\n    \n    if [[ -z \"$email\" ]]; then\n        warn \"Alert email not configured, skipping email notification\"\n        return 0\n    fi\n    \n    if command -v mail > /dev/null 2>&1; then\n        echo \"$message\" | mail -s \"$subject\" \"$email\" || warn \"Failed to send email notification\"\n    else\n        warn \"Mail command not available, skipping email notification\"\n    fi\n}\n\n# Health check function\ncheck_application_health() {\n    local port=${1:-5000}\n    local timeout=${2:-10}\n    \n    # Basic HTTP health check\n    if ! curl -f -s --max-time \"$timeout\" \"http://localhost:${port}/\" > /dev/null 2>&1; then\n        return 1\n    fi\n    \n    return 0\n}\n\n# Container health check\ncheck_container_health() {\n    local container_name=\"${1:-fes_crm_prod}\"\n    \n    # Check if container is running\n    if ! docker ps --filter \"name=$container_name\" --filter \"status=running\" | grep -q \"$container_name\"; then\n        return 1\n    fi\n    \n    # Check container health status\n    local health_status=$(docker inspect --format='{{.State.Health.Status}}' \"$container_name\" 2>/dev/null || echo \"unknown\")\n    \n    if [[ \"$health_status\" != \"healthy\" ]] && [[ \"$health_status\" != \"unknown\" ]]; then\n        return 1\n    fi\n    \n    return 0\n}\n\n# Database connectivity check\ncheck_database_connectivity() {\n    local container_name=\"${1:-fes_crm_prod}\"\n    \n    # Test database connection through container\n    if ! docker exec \"$container_name\" npx prisma db pull --schema=./prisma/schema.prisma > /dev/null 2>&1; then\n        return 1\n    fi\n    \n    return 0\n}\n\n# Resource usage check\ncheck_resource_usage() {\n    local container_name=\"${1:-fes_crm_prod}\"\n    \n    # Get container stats\n    local stats=$(docker stats --no-stream --format \"table {{.MemPerc}}\\t{{.CPUPerc}}\" \"$container_name\" 2>/dev/null | tail -1)\n    \n    if [[ -z \"$stats\" ]]; then\n        return 1\n    fi\n    \n    local memory_usage=$(echo \"$stats\" | awk '{print $1}' | sed 's/%//')\n    local cpu_usage=$(echo \"$stats\" | awk '{print $2}' | sed 's/%//')\n    \n    # Check memory usage (alert if > 90%)\n    if (( $(echo \"$memory_usage > 90\" | bc -l) )); then\n        warn \"High memory usage detected: ${memory_usage}%\"\n        send_slack_alert \"High memory usage detected: ${memory_usage}%\" \"warning\"\n    fi\n    \n    # Check CPU usage (alert if > 95%)\n    if (( $(echo \"$cpu_usage > 95\" | bc -l) )); then\n        warn \"High CPU usage detected: ${cpu_usage}%\"\n        send_slack_alert \"High CPU usage detected: ${cpu_usage}%\" \"warning\"\n    fi\n    \n    return 0\n}\n\n# Disk space check\ncheck_disk_space() {\n    local threshold=${1:-90}\n    \n    # Check disk usage\n    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')\n    \n    if (( disk_usage > threshold )); then\n        error \"Low disk space: ${disk_usage}% used\"\n        send_slack_alert \"Low disk space warning: ${disk_usage}% used\" \"critical\"\n        return 1\n    fi\n    \n    return 0\n}\n\n# Log rotation check\ncheck_log_rotation() {\n    local log_dir=\"${1:-/var/log}\"\n    local max_size_mb=${2:-1000}\n    \n    # Check log file sizes\n    find \"$log_dir\" -name \"*.log\" -size \"+${max_size_mb}M\" | while read -r logfile; do\n        warn \"Large log file detected: $logfile\"\n        # Rotate log if logrotate is available\n        if command -v logrotate > /dev/null 2>&1; then\n            logrotate -f /etc/logrotate.conf 2>/dev/null || true\n        fi\n    done\n}\n\n# Performance metrics collection\ncollect_performance_metrics() {\n    local container_name=\"${1:-fes_crm_prod}\"\n    local metrics_file=\"/tmp/fes-crm-metrics.json\"\n    \n    # Collect container metrics\n    local stats=$(docker stats --no-stream --format \"json\" \"$container_name\" 2>/dev/null || echo '{}')\n    \n    # Collect system metrics\n    local system_metrics=$(cat << EOF\n{\n  \"timestamp\": \"$(date -Iseconds)\",\n  \"hostname\": \"$(hostname)\",\n  \"uptime\": \"$(uptime -p)\",\n  \"load_average\": \"$(uptime | awk -F'load average:' '{print $2}')\",\n  \"memory_total\": \"$(free -m | awk 'NR==2{print $2}')\",\n  \"memory_used\": \"$(free -m | awk 'NR==2{print $3}')\",\n  \"disk_usage\": \"$(df / | tail -1 | awk '{print $5}')\",\n  \"container_stats\": $stats\n}\nEOF\n    )\n    \n    echo \"$system_metrics\" > \"$metrics_file\"\n    \n    # Send metrics to monitoring system if configured\n    if [[ -n \"${METRICS_ENDPOINT:-}\" ]]; then\n        curl -X POST -H \"Content-Type: application/json\" \\\n            -d \"$system_metrics\" \\\n            \"$METRICS_ENDPOINT\" > /dev/null 2>&1 || warn \"Failed to send metrics\"\n    fi\n}\n\n# Main monitoring function\nmonitor_application() {\n    local failure_count=0\n    local container_name=\"fes_crm_prod\"\n    \n    info \"Starting application monitoring for $APP_NAME\"\n    \n    while true; do\n        local health_ok=true\n        \n        # Application health check\n        if ! check_application_health; then\n            error \"Application health check failed\"\n            health_ok=false\n        fi\n        \n        # Container health check\n        if ! check_container_health \"$container_name\"; then\n            error \"Container health check failed\"\n            health_ok=false\n        fi\n        \n        # Database connectivity check\n        if ! check_database_connectivity \"$container_name\"; then\n            error \"Database connectivity check failed\"\n            health_ok=false\n        fi\n        \n        # Resource usage check\n        check_resource_usage \"$container_name\"\n        \n        # Disk space check\n        check_disk_space\n        \n        # Log rotation check\n        check_log_rotation\n        \n        # Collect performance metrics\n        collect_performance_metrics \"$container_name\"\n        \n        if [[ \"$health_ok\" == \"false\" ]]; then\n            ((failure_count++))\n            warn \"Health check failed (failure count: $failure_count)\"\n            \n            if (( failure_count >= ALERT_THRESHOLD )); then\n                error \"Application health check failed $failure_count times consecutively\"\n                send_slack_alert \"Application health check failed $failure_count times consecutively\" \"critical\"\n                send_email_alert \"FES CRM Backend Alert\" \"Application health check failed $failure_count times consecutively\"\n                \n                # Attempt automatic recovery\n                info \"Attempting automatic recovery...\"\n                docker compose -f \"$PROJECT_ROOT/docker-compose.prod.yml\" restart\n                \n                # Reset failure count after recovery attempt\n                failure_count=0\n                \n                # Wait longer after recovery attempt\n                sleep $((HEALTH_CHECK_INTERVAL * 2))\n                continue\n            fi\n        else\n            if (( failure_count > 0 )); then\n                log \"Application health restored after $failure_count failures\"\n                send_slack_alert \"Application health restored after $failure_count failures\" \"success\"\n            fi\n            failure_count=0\n        fi\n        \n        sleep \"$HEALTH_CHECK_INTERVAL\"\n    done\n}\n\n# Deployment validation function\nvalidate_deployment() {\n    local version=\"${1:-unknown}\"\n    local timeout=${2:-300}\n    local start_time=$(date +%s)\n    \n    info \"Validating deployment of version $version\"\n    \n    while true; do\n        local current_time=$(date +%s)\n        local elapsed=$((current_time - start_time))\n        \n        if (( elapsed > timeout )); then\n            error \"Deployment validation timeout after ${timeout}s\"\n            return 1\n        fi\n        \n        # Check all health indicators\n        if check_application_health && \\\n           check_container_health && \\\n           check_database_connectivity; then\n            log \"Deployment validation successful for version $version\"\n            send_slack_alert \"Deployment validation successful for version $version\" \"success\"\n            return 0\n        fi\n        \n        info \"Waiting for deployment to stabilize... (${elapsed}s/${timeout}s)\"\n        sleep 10\n    done\n}\n\n# Help function\nshow_help() {\n    cat << EOF\nDeployment Monitoring Script for FES CRM Backend\n\nUsage: $0 [COMMAND] [OPTIONS]\n\nCOMMANDS:\n    monitor                 Start continuous monitoring (default)\n    validate VERSION        Validate deployment of specific version\n    check                   Run single health check\n    metrics                 Collect and display current metrics\n    help                    Show this help message\n\nOPTIONS:\n    -i, --interval SECONDS  Health check interval (default: 30)\n    -t, --threshold COUNT   Alert threshold (default: 3)\n    -l, --log-file FILE     Log file path (default: /var/log/fes-crm-monitor.log)\n\nEXAMPLES:\n    $0                      # Start monitoring with defaults\n    $0 monitor -i 60        # Monitor with 60s interval\n    $0 validate abc123      # Validate deployment of version abc123\n    $0 check                # Run single health check\n    $0 metrics              # Show current metrics\n\nEOF\n}\n\n# Parse command line arguments\nCOMMAND=\"monitor\"\n\nwhile [[ $# -gt 0 ]]; do\n    case $1 in\n        monitor|validate|check|metrics|help)\n            COMMAND=\"$1\"\n            shift\n            ;;\n        -i|--interval)\n            HEALTH_CHECK_INTERVAL=\"$2\"\n            shift 2\n            ;;\n        -t|--threshold)\n            ALERT_THRESHOLD=\"$2\"\n            shift 2\n            ;;\n        -l|--log-file)\n            LOG_FILE=\"$2\"\n            shift 2\n            ;;\n        *)\n            if [[ \"$COMMAND\" == \"validate\" ]] && [[ -z \"${VERSION:-}\" ]]; then\n                VERSION=\"$1\"\n                shift\n            else\n                error \"Unknown option: $1\"\n                show_help\n                exit 1\n            fi\n            ;;\n    esac\ndone\n\n# Create log directory if it doesn't exist\nmkdir -p \"$(dirname \"$LOG_FILE\")\"\n\n# Execute command\ncase \"$COMMAND\" in\n    \"monitor\")\n        monitor_application\n        ;;\n    \"validate\")\n        if [[ -z \"${VERSION:-}\" ]]; then\n            error \"Version is required for validation\"\n            show_help\n            exit 1\n        fi\n        validate_deployment \"$VERSION\"\n        ;;\n    \"check\")\n        info \"Running single health check...\"\n        if check_application_health && check_container_health && check_database_connectivity; then\n            log \"All health checks passed\"\n            exit 0\n        else\n            error \"Health checks failed\"\n            exit 1\n        fi\n        ;;\n    \"metrics\")\n        info \"Collecting current metrics...\"\n        collect_performance_metrics\n        cat \"/tmp/fes-crm-metrics.json\"\n        ;;\n    \"help\")\n        show_help\n        ;;\n    *)\n        error \"Unknown command: $COMMAND\"\n        show_help\n        exit 1\n        ;;\nesac
