# Production Environment Variables Template
# Copy this to .env on your production server and update the values

# Application
NODE_ENV=production
BACKEND_PORT=5000
ENABLE_AUTH=TRUE
JWT_SECRET=your-super-secure-jwt-secret-here
JWT_EXPIRES=7d

# External Database Configuration
# Update these values with your external PostgreSQL database details
DB_HOST=your-external-db-host
DB_PORT=5432
DB_NAME=fes_crm_prod
DB_USER=your-db-username
DB_PASSWORD=your-db-password

# Full database URL (update with your external database details)
DATABASE_URL="postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Optional: Email configuration (if using SendGrid)
# SENDGRID_API_KEY=your-sendgrid-api-key

# Optional: File upload configuration
# MAX_FILE_SIZE=10485760
