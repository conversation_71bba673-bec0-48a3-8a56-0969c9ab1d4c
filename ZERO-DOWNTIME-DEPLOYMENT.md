# Zero-Downtime Production Deployment Guide

This guide explains the comprehensive zero-downtime deployment system for FES CRM Backend with industry-standard practices.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   GitHub        │    │   Production     │    │   External          │
│   Actions       │───▶│   Server         │───▶│   PostgreSQL        │
│   (main branch) │    │   (Blue-Green)   │    │   Database          │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Load Balancer  │
                       │   (Traefik)      │
                       └──────────────────┘
```

## 🎯 Zero-Downtime Features

### 1. Blue-Green Deployment Strategy
- **Blue Environment**: Current production version
- **Green Environment**: New version being deployed
- **Traffic Switch**: Instant cutover after health checks pass
- **Automatic Rollback**: If health checks fail

### 2. Comprehensive Health Checks
- **Application Health**: HTTP endpoint monitoring
- **Database Connectivity**: Connection and query tests
- **Container Health**: Docker health status
- **Resource Monitoring**: Memory and CPU usage
- **Smoke Tests**: API functionality validation

### 3. Safety Mechanisms
- **Database Migration Safety**: Non-destructive migrations only
- **Backup Creation**: Automatic backup before deployment
- **Emergency Rollback**: Instant rollback on failure
- **Deployment Validation**: Post-deployment verification

## 📋 Prerequisites

1. **Production Server** with Docker and Docker Compose installed
2. **External PostgreSQL Database** accessible from production server
3. **GitHub Secrets** configured for deployment
4. **Slack Webhook** (optional) for notifications
5. **Email Configuration** (optional) for alerts

## 🔧 Setup Steps

### 1. Configure GitHub Secrets

Add these secrets in your GitHub repository settings:

```
PROD_HOST=your-production-server-ip
PROD_USERNAME=ubuntu
PROD_SSH_KEY=your-ssh-private-key
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### 2. Prepare Production Server

```bash
# Install Docker and Docker Compose
sudo apt update
sudo apt install docker.io docker-compose-plugin curl bc

# Create application directory
sudo mkdir -p /home/<USER>/fes-crm-backend
sudo chown ubuntu:ubuntu /home/<USER>/fes-crm-backend

# Create log directory
sudo mkdir -p /var/log
sudo chown ubuntu:ubuntu /var/log
```

### 3. Configure Environment Variables

Create `.env` file on production server:

```bash
cd /home/<USER>/fes-crm-backend
cp .env.production .env
# Edit .env with your actual configuration
nano .env
```

Required variables:
```env
DATABASE_URL="**********************************************/database_name"
JWT_SECRET=your-super-secure-jwt-secret
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
PROD_HOST=your-production-domain.com
```

## 🚀 Deployment Process

### Automatic Zero-Downtime Deployment

1. **Security Scan**: Vulnerability scanning with Trivy
2. **Build & Test**: Comprehensive testing and Docker image build
3. **Blue-Green Deploy**: 
   - Start green environment on port 5001
   - Run health checks and smoke tests
   - Switch traffic from blue (5000) to green
   - Cleanup old environment
4. **Validation**: Post-deployment integration tests
5. **Monitoring**: Continuous health monitoring
6. **Notifications**: Slack/email alerts

### Manual Deployment Scripts

```bash
# Zero-downtime deployment
./scripts/zero-downtime-deploy.sh -v latest

# With custom options
./scripts/zero-downtime-deploy.sh -v abc123 -t 900 -r 50

# Dry run
./scripts/zero-downtime-deploy.sh -v latest -d
```

## 📊 Monitoring & Alerting

### Continuous Monitoring

```bash
# Start monitoring daemon
./scripts/deployment-monitor.sh monitor

# Single health check
./scripts/deployment-monitor.sh check

# Validate specific deployment
./scripts/deployment-monitor.sh validate abc123

# View current metrics
./scripts/deployment-monitor.sh metrics
```

### Health Check Endpoints

- **Application**: `http://localhost:5000/`
- **Load Balancer**: `http://localhost:80/`
- **Container Health**: `docker inspect fes_crm_prod`

### Alert Thresholds

- **Memory Usage**: Alert at 90%
- **CPU Usage**: Alert at 95%
- **Disk Space**: Alert at 90%
- **Health Check Failures**: Alert after 3 consecutive failures

## 🔄 Rollback Procedures

### Automatic Rollback
- Triggered automatically on deployment failure
- Uses latest backup for restoration
- Includes health verification

### Manual Rollback

```bash
# List available backups
./scripts/rollback.sh -l

# Rollback to specific version
./scripts/rollback.sh -v abc123

# Rollback to specific backup
./scripts/rollback.sh -b backup-20240101-120000

# Force rollback without confirmation
./scripts/rollback.sh -f

# Dry run rollback
./scripts/rollback.sh -v abc123 -d
```

## 🔒 Security Features

### Container Security
- **Non-root user**: Application runs as `nestjs` user
- **Read-only filesystem**: Limited write access
- **Security options**: `no-new-privileges:true`
- **Resource limits**: CPU and memory constraints
- **Network isolation**: Custom Docker network

### Vulnerability Scanning
- **Trivy scanner**: Scans for critical and high vulnerabilities
- **SARIF reports**: Uploaded to GitHub Security tab
- **Deployment blocking**: Stops deployment on critical issues
- **Force override**: Available for emergency deployments

### Access Control
- **SSH key authentication**: No password access
- **Environment protection**: GitHub environment protection rules
- **Secrets management**: Encrypted GitHub secrets
- **Audit logging**: All deployment actions logged

## 🗂️ File Structure

```
fes-crm-backend/
├── .github/workflows/
│   └── production.yml           # Zero-downtime deployment workflow
├── docker-compose.prod.yml      # Enhanced production compose
├── Dockerfile.prod             # Multi-stage production build
├── entrypoint.prod.sh          # Production entrypoint
├── .env.production             # Environment template
├── scripts/
│   ├── zero-downtime-deploy.sh # Manual deployment script
│   ├── deployment-monitor.sh   # Monitoring and alerting
│   └── rollback.sh            # Rollback procedures
└── ZERO-DOWNTIME-DEPLOYMENT.md # This documentation
```

## 🚨 Troubleshooting

### Deployment Failures

```bash
# Check deployment logs
sudo docker compose -f docker-compose.prod.yml logs

# Check GitHub Actions logs
# Visit: https://github.com/your-repo/actions

# Manual health check
curl -v http://localhost:5000/

# Check container status
sudo docker compose -f docker-compose.prod.yml ps
```

### Database Issues

```bash
# Test database connection
sudo docker compose -f docker-compose.prod.yml exec backend npx prisma db pull

# Check migration status
sudo docker compose -f docker-compose.prod.yml exec backend npx prisma migrate status

# View database logs (if using Docker database)
sudo docker logs fes_crm_db
```

### Performance Issues

```bash
# Check resource usage
sudo docker stats

# View application metrics
./scripts/deployment-monitor.sh metrics

# Check system resources
htop
df -h
free -m
```

### Network Issues

```bash
# Check port availability
sudo netstat -tlnp | grep :5000

# Test load balancer
curl -v http://localhost:80/

# Check Docker networks
sudo docker network ls
sudo docker network inspect fes_crm_network
```

## 📈 Performance Optimization

### Docker Optimizations
- **Multi-stage build**: Reduces image size by ~60%
- **Layer caching**: Optimized Dockerfile layer order
- **Production dependencies**: Only production packages
- **Resource limits**: Prevents resource exhaustion

### Application Optimizations
- **Node.js tuning**: Optimized memory settings
- **Connection pooling**: Database connection optimization
- **Caching**: Redis integration ready
- **Compression**: Response compression enabled

### Infrastructure Optimizations
- **Load balancing**: Traefik for traffic distribution
- **Health checks**: Optimized check intervals
- **Log rotation**: Automatic log management
- **Monitoring**: Real-time performance metrics

## 🔧 Maintenance

### Regular Tasks

```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Clean Docker resources
sudo docker system prune -f

# Rotate logs
sudo logrotate -f /etc/logrotate.conf

# Check disk space
df -h
```

### Backup Management

```bash
# List backups
ls -la ../fes-crm-backend-backup-*

# Manual backup
cp -r . ../fes-crm-backend-backup-$(date +%Y%m%d-%H%M%S)

# Cleanup old backups (keeps last 5)
find .. -name "fes-crm-backend-backup-*" -type d | sort | head -n -5 | xargs rm -rf
```

### Security Updates

```bash
# Update base images
sudo docker pull node:20-alpine
sudo docker pull traefik:v3.0
sudo docker pull postgres:15-alpine

# Rebuild with updated images
sudo docker compose -f docker-compose.prod.yml build --no-cache
```

## 📞 Support

### Emergency Contacts
- **DevOps Team**: <EMAIL>
- **On-call Engineer**: +1-xxx-xxx-xxxx
- **Slack Channel**: #fes-crm-alerts

### Escalation Procedures
1. **Level 1**: Automatic rollback (if configured)
2. **Level 2**: Manual rollback using scripts
3. **Level 3**: Emergency manual intervention
4. **Level 4**: Database restoration from backup

### Documentation
- **GitHub Repository**: https://github.com/your-org/fes-crm-backend
- **Deployment Logs**: `/var/log/fes-crm-monitor.log`
- **Application Logs**: `docker logs fes_crm_prod`
- **Monitoring Dashboard**: https://your-monitoring-url.com
